import React, { useState, useEffect } from 'react';
import { useCameraStore } from '../../../store/cameraStore';
import useCameraSettingsStore from '../../../store/cameraSettingsStore';
import cameraSettingsApi from '../../../services/cameraSettingsApi';
import '../CameraSettings.css';

const CameraSettings = () => {
  const [selectedCameraIP, setSelectedCameraIP] = useState('');
  const [availableCameras, setAvailableCameras] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [credentials, setCredentials] = useState({
    username: 'admin',
    password: 'admin',
    port: 80
  });
  const [credentialsSource, setCredentialsSource] = useState(''); // 'stored', 'collection', or 'manual'

  // Video stream settings state variables (including resolution)
  const [selectedResolution, setSelectedResolution] = useState('');
  const [selectedStreamType, setSelectedStreamType] = useState('');
  const [selectedVideoType, setSelectedVideoType] = useState('');
  const [selectedBitrateType, setSelectedBitrateType] = useState('');
  const [selectedVideoQuality, setSelectedVideoQuality] = useState('');
  const [selectedFrameRate, setSelectedFrameRate] = useState(25);
  const [selectedMaxBitrate, setSelectedMaxBitrate] = useState(2048);
  const [selectedVideoEncoding, setSelectedVideoEncoding] = useState('');
  const [selectedH265Plus, setSelectedH265Plus] = useState('');
  const [selectedProfile, setSelectedProfile] = useState('');
  const [selectedIFrameInterval, setSelectedIFrameInterval] = useState(50);
  const [selectedSVC, setSelectedSVC] = useState('');
  const [selectedSmoothing, setSelectedSmoothing] = useState(50);

  const { cameras, cameraJson } = useCameraStore();
  const {
    cameraSettings,
    supportedResolutions,
    currentResolutions,
    connectionStatus,
    currentStreamSettings: currentStreamSettingsStore,
    supportedStreamSettings: supportedStreamSettingsStore,
    setCameraSettings,
    setSupportedResolutions,
    setCurrentResolution,
    setConnectionStatus,
    setCurrentStreamSettings,
    setSupportedStreamSettings,
    getSupportedResolutions,
    getCurrentResolution,
    getConnectionStatus,
    getCurrentStreamSettings,
    getSupportedStreamSettings
  } = useCameraSettingsStore();

  // Extract unique camera IPs from the camera store
  useEffect(() => {
    const extractCameraIPs = () => {
      const cameraIPs = new Set();

      // From cameras array
      cameras.forEach(camera => {
        if (camera.ip) {
          cameraIPs.add(camera.ip);
        }
      });

      // From cameraJson configuration
      Object.values(cameraJson).forEach(collection => {
        if (typeof collection === 'object') {
          Object.keys(collection).forEach(ip => {
            cameraIPs.add(ip);
          });
        }
      });

      return Array.from(cameraIPs).sort();
    };

    setAvailableCameras(extractCameraIPs());
  }, [cameras, cameraJson]);

  // Load camera settings when camera is selected
  useEffect(() => {
    if (selectedCameraIP) {
      // First try to load from stored camera settings
      if (cameraSettings[selectedCameraIP]?.onvifCredentials) {
        const settings = cameraSettings[selectedCameraIP];
        setCredentials(settings.onvifCredentials);
        setCredentialsSource('stored');
      } else {
        // Extract credentials from camera collection data
        const extractedCredentials = extractCredentialsFromCollections(selectedCameraIP);
        if (extractedCredentials) {
          setCredentials(extractedCredentials);
          setCredentialsSource('collection');
        }
      }
    }
  }, [selectedCameraIP, cameraSettings, cameraJson]);

  // Populate form fields with current stream settings and resolution when they are loaded
  useEffect(() => {
    if (selectedCameraIP && currentStreamSettings) {
      const settings = currentStreamSettings;

      // Update all form fields with current settings from the camera
      if (settings.streamType !== undefined) setSelectedStreamType(settings.streamType);
      if (settings.videoType !== undefined) setSelectedVideoType(settings.videoType);
      if (settings.bitrateType !== undefined) setSelectedBitrateType(settings.bitrateType);
      if (settings.videoQuality !== undefined) setSelectedVideoQuality(settings.videoQuality);
      if (settings.frameRate !== undefined) setSelectedFrameRate(settings.frameRate);
      if (settings.maxBitrate !== undefined) setSelectedMaxBitrate(settings.maxBitrate);
      if (settings.videoEncoding !== undefined) setSelectedVideoEncoding(settings.videoEncoding);
      if (settings.h265Plus !== undefined) setSelectedH265Plus(settings.h265Plus);
      if (settings.profile !== undefined) setSelectedProfile(settings.profile);
      if (settings.iFrameInterval !== undefined) setSelectedIFrameInterval(settings.iFrameInterval);
      if (settings.svc !== undefined) setSelectedSVC(settings.svc);
      if (settings.smoothing !== undefined) setSelectedSmoothing(settings.smoothing);
    }
  }, [selectedCameraIP, currentStreamSettings]);

  // Populate resolution field when current resolution is loaded
  useEffect(() => {
    if (selectedCameraIP && currentRes) {
      setSelectedResolution(`${currentRes.width}x${currentRes.height}`);
    }
  }, [selectedCameraIP, currentRes]);

  // Function to extract credentials from RTSP URL in collection data
  const extractCredentialsFromCollections = (cameraIP) => {
    try {
      // Search through all collections for this camera IP
      for (const [collectionName, cameras] of Object.entries(cameraJson)) {
        if (typeof cameras === 'object' && cameras[cameraIP]) {
          const rtspUrl = cameras[cameraIP];

          // Extract credentials from RTSP URL if present
          if (rtspUrl && rtspUrl.includes('://') && rtspUrl.includes('@')) {
            try {
              // Parse RTSP URL: rtsp://username:password@ip:port/path
              const urlParts = rtspUrl.split('://');
              if (urlParts.length > 1) {
                const authPart = urlParts[1].split('@')[0];
                if (authPart.includes(':')) {
                  const [username, password] = authPart.split(':', 2);
                  return {
                    username: decodeURIComponent(username),
                    password: decodeURIComponent(password),
                    port: 80 // Default ONVIF port
                  };
                }
              }
            } catch (error) {
              console.warn(`Failed to parse credentials from RTSP URL for ${cameraIP}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.error('Error extracting credentials from collections:', error);
    }

    // Return default credentials if extraction fails
    return {
      username: 'admin',
      password: 'admin',
      port: 80
    };
  };

  const handleCameraSelect = (event) => {
    const ip = event.target.value;
    setSelectedCameraIP(ip);
    setError('');
    setSuccess('');

    // Reset video settings to default values when camera changes
    setSelectedResolution('');
    setSelectedStreamType('');
    setSelectedVideoType('');
    setSelectedBitrateType('');
    setSelectedVideoQuality('');
    setSelectedFrameRate(25);
    setSelectedMaxBitrate(2048);
    setSelectedVideoEncoding('');
    setSelectedH265Plus('');
    setSelectedProfile('');
    setSelectedIFrameInterval(50);
    setSelectedSVC('');
    setSelectedSmoothing(50);

    // Auto-fill credentials from stored settings or collection data
    if (ip) {
      if (cameraSettings[ip]?.onvifCredentials) {
        // Use stored ONVIF credentials if available
        setCredentials(cameraSettings[ip].onvifCredentials);
        setCredentialsSource('stored');
      } else {
        // Extract credentials from collection data
        const extractedCredentials = extractCredentialsFromCollections(ip);
        setCredentials(extractedCredentials);
        setCredentialsSource('collection');
      }
    } else {
      // Reset to defaults when no camera selected
      setCredentials({
        username: 'admin',
        password: 'admin',
        port: 80
      });
      setCredentialsSource('');
    }
  };

  const handleCredentialChange = (field, value) => {
    setCredentials(prev => ({
      ...prev,
      [field]: value
    }));
    setCredentialsSource('manual'); // Mark as manually edited
  };

  const testConnection = async () => {
    if (!selectedCameraIP) {
      setError('Please select a camera first');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');
    setConnectionStatus(selectedCameraIP, 'connecting');

    try {
      const result = await cameraSettingsApi.testOnvifConnection({
        ip: selectedCameraIP,
        username: credentials.username,
        password: credentials.password,
        port: credentials.port
      });

      if (result.success) {
        setSuccess(result.message);
        setConnectionStatus(selectedCameraIP, 'connected');

        // Update credentials with working port
        const updatedCredentials = {
          ...credentials,
          port: result.port_used
        };
        setCredentials(updatedCredentials);

        // Save credentials to store
        setCameraSettings(selectedCameraIP, {
          onvifCredentials: updatedCredentials
        });

        // Load current resolution and supported resolutions
        await loadCameraInfo();
      } else {
        setError(result.message);
        setConnectionStatus(selectedCameraIP, 'error');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      setError('Failed to test connection. Please check camera IP and credentials.');
      setConnectionStatus(selectedCameraIP, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadCameraInfo = async () => {
    if (!selectedCameraIP) return;

    try {
      // Get current resolution
      const currentRes = await cameraSettingsApi.getCurrentResolution(
        selectedCameraIP,
        credentials.username,
        credentials.password,
        credentials.port
      );

      if (currentRes.success) {
        setCurrentResolution(selectedCameraIP, currentRes.data);
      }

      // Get supported resolutions
      const supportedRes = await cameraSettingsApi.getSupportedResolutions(
        selectedCameraIP,
        credentials.username,
        credentials.password,
        credentials.port
      );

      if (supportedRes.success) {
        setSupportedResolutions(selectedCameraIP, supportedRes.data);
      }

      // Get current stream settings
      const currentStreamSettings = await cameraSettingsApi.getCurrentStreamSettings(
        selectedCameraIP,
        credentials.username,
        credentials.password,
        credentials.port
      );

      if (currentStreamSettings.success) {
        setCurrentStreamSettings(selectedCameraIP, currentStreamSettings.data);
      }

      // Get supported stream settings
      const supportedStreamSettings = await cameraSettingsApi.getSupportedStreamSettings(
        selectedCameraIP,
        credentials.username,
        credentials.password,
        credentials.port
      );

      if (supportedStreamSettings.success) {
        setSupportedStreamSettings(selectedCameraIP, supportedStreamSettings.data);
      }
    } catch (error) {
      console.error('Error loading camera info:', error);
    }
  };


  const applyVideoSettings = async () => {
    if (!selectedCameraIP) {
      setError('Please select a camera first');
      return;
    }

    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      let resolutionChanged = false;
      let streamSettingsChanged = false;

      // Apply resolution change if selected
      if (selectedResolution && selectedResolution !== `${currentRes?.width}x${currentRes?.height}`) {
        const [width, height] = selectedResolution.split('x').map(Number);

        const resolutionResult = await cameraSettingsApi.setCameraResolution({
          ip: selectedCameraIP,
          username: credentials.username,
          password: credentials.password,
          port: credentials.port,
          width,
          height,
          frameRate: selectedFrameRate || 15,
          bitrate: selectedMaxBitrate || 1024
        });

        if (resolutionResult.success) {
          resolutionChanged = true;
        } else {
          setError(`Resolution change failed: ${resolutionResult.message}`);
          return;
        }
      }

      // Apply stream settings
      const streamSettingsRequest = {
        ip: selectedCameraIP,
        username: credentials.username,
        password: credentials.password,
        port: credentials.port,
        streamType: selectedStreamType,
        videoType: selectedVideoType,
        bitrateType: selectedBitrateType,
        videoQuality: selectedVideoQuality,
        frameRate: selectedFrameRate,
        maxBitrate: selectedMaxBitrate,
        videoEncoding: selectedVideoEncoding,
        h265Plus: selectedH265Plus,
        profile: selectedProfile,
        iFrameInterval: selectedIFrameInterval,
        svc: selectedSVC,
        smoothing: selectedSmoothing
      };

      const streamResult = await cameraSettingsApi.setCameraStreamSettings(streamSettingsRequest);

      if (streamResult.success) {
        streamSettingsChanged = true;
      } else {
        setError(`Stream settings change failed: ${streamResult.message}`);
        return;
      }

      // Success message
      let successMessage = '';
      if (resolutionChanged && streamSettingsChanged) {
        successMessage = 'Resolution and stream settings updated successfully';
      } else if (resolutionChanged) {
        successMessage = 'Resolution updated successfully';
      } else if (streamSettingsChanged) {
        successMessage = 'Stream settings updated successfully';
      }

      setSuccess(successMessage);
      // Reload current settings
      await loadCameraInfo();

    } catch (error) {
      console.error('Video settings change error:', error);
      setError('Failed to apply video settings. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const currentRes = getCurrentResolution(selectedCameraIP);
  const supportedRes = getSupportedResolutions(selectedCameraIP);
  const connectionStat = getConnectionStatus(selectedCameraIP);
  const currentStreamSettings = getCurrentStreamSettings(selectedCameraIP);
  const supportedStreamSettings = getSupportedStreamSettings(selectedCameraIP);

  return (
    <div className="camera-settings">
      <div className="settings-header">
        <h2>Camera Settings</h2>
        <p>Configure camera resolution and ONVIF settings for your IP cameras.</p>
      </div>

      <div className="settings-content">
        <div className="settings-section">
          <h3>Camera Selection</h3>
          <div className="form-group">
            <label htmlFor="camera-select">Select Camera IP Address</label>
            <select
              id="camera-select"
              className="form-control"
              value={selectedCameraIP}
              onChange={handleCameraSelect}
              disabled={isLoading}
            >
              <option value="">-- Select Camera IP --</option>
              {availableCameras.map(ip => (
                <option key={ip} value={ip}>{ip}</option>
              ))}
            </select>
          </div>

          {availableCameras.length === 0 && (
            <p className="credential-note">
              No cameras found. Please add cameras to your collections first.
            </p>
          )}
        </div>

        {selectedCameraIP && (
          <>
            <div className="settings-section">
              <h3>ONVIF Credentials</h3>
              {credentialsSource === 'collection' && (
                <div className="alert alert-success" style={{ marginBottom: '1rem' }}>
                  <strong>Auto-filled:</strong> Credentials loaded from camera collection data.
                </div>
              )}
              {credentialsSource === 'stored' && (
                <div className="alert alert-success" style={{ marginBottom: '1rem' }}>
                  <strong>Loaded:</strong> Using previously saved ONVIF credentials.
                </div>
              )}
              <div className="credentials-form">
                <div className="form-row">
                  <div className="form-group">
                    <label htmlFor="username">Username</label>
                    <input
                      id="username"
                      type="text"
                      className="form-control"
                      value={credentials.username}
                      onChange={(e) => handleCredentialChange('username', e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="password">Password</label>
                    <input
                      id="password"
                      type="password"
                      className="form-control"
                      value={credentials.password}
                      onChange={(e) => handleCredentialChange('password', e.target.value)}
                      disabled={isLoading}
                    />
                  </div>
                  <div className="form-group">
                    <label htmlFor="port">Port</label>
                    <input
                      id="port"
                      type="number"
                      className="form-control"
                      value={credentials.port}
                      onChange={(e) => handleCredentialChange('port', parseInt(e.target.value))}
                      disabled={isLoading}
                    />
                  </div>
                </div>

                <div className="button-group">
                  <button
                    className="btn btn-primary"
                    onClick={testConnection}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Testing...' : 'Test Connection'}
                  </button>

                  <div className={`connection-status status-${connectionStat}`}>
                    {connectionStat}
                  </div>
                </div>
              </div>

              <div className="credential-hints">
                <h4>Credential Options</h4>
                <div className="credential-options">
                  <div
                    className="credential-option"
                    onClick={() => {
                      setCredentials({username: 'admin', password: 'admin', port: 80});
                      setCredentialsSource('manual');
                    }}
                  >
                    admin / admin
                  </div>
                  <div
                    className="credential-option"
                    onClick={() => {
                      setCredentials({username: 'admin', password: '12345', port: 80});
                      setCredentialsSource('manual');
                    }}
                  >
                    admin / 12345
                  </div>
                  <div
                    className="credential-option"
                    onClick={() => {
                      setCredentials({username: 'root', password: 'admin', port: 80});
                      setCredentialsSource('manual');
                    }}
                  >
                    root / admin
                  </div>
                  <div
                    className="credential-option"
                    onClick={() => {
                      const extractedCredentials = extractCredentialsFromCollections(selectedCameraIP);
                      setCredentials(extractedCredentials);
                      setCredentialsSource('collection');
                    }}
                  >
                    📋 Reload from Collection
                  </div>
                </div>
                <p className="credential-note">
                  Credentials are automatically loaded from camera collection data when available.
                  Click options above to use common credentials or reload from collection.
                </p>
              </div>
            </div>



            {connectionStat === 'connected' && (
              <div className="settings-section">
                <h3>Video Stream Settings</h3>

                {(currentRes || currentStreamSettings) && (
                  <div className="current-stream-settings">
                    <span>Current Settings:</span>
                    <div className="current-settings-grid">
                      <span>Resolution: {currentRes ? `${currentRes.width}x${currentRes.height} (${cameraSettingsApi.formatResolution(currentRes.width, currentRes.height)})` : 'N/A'}</span>
                      <span>Stream Type: {currentStreamSettings?.streamType || 'N/A'}</span>
                      <span>Video Type: {currentStreamSettings?.videoType || 'N/A'}</span>
                      <span>Bitrate Type: {currentStreamSettings?.bitrateType || 'N/A'}</span>
                      <span>Video Quality: {currentStreamSettings?.videoQuality || 'N/A'}</span>
                      <span>Frame Rate: {currentStreamSettings?.frameRate || 'N/A'}{typeof currentStreamSettings?.frameRate === 'number' ? ' fps' : ''}</span>
                      <span>Max Bitrate: {currentStreamSettings?.maxBitrate || 'N/A'} Kbps</span>
                      <span>Video Encoding: {currentStreamSettings?.videoEncoding || 'N/A'}</span>
                      <span>H.265+: {currentStreamSettings?.h265Plus || 'N/A'}</span>
                      <span>Profile: {currentStreamSettings?.profile || 'N/A'}</span>
                      <span>I Frame Interval: {currentStreamSettings?.iFrameInterval || 'N/A'}</span>
                      <span>SVC: {currentStreamSettings?.svc || 'N/A'}</span>
                      <span>Smoothing: {currentStreamSettings?.smoothing || 'N/A'}</span>
                    </div>
                  </div>
                )}

                <div className="stream-settings-form">
                  <div className="settings-grid">
                    {/* Resolution */}
                    <div className="form-group">
                      <label htmlFor="resolution-select">Resolution</label>
                      <select
                        id="resolution-select"
                        className="form-control"
                        value={selectedResolution}
                        onChange={(e) => setSelectedResolution(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Resolution --</option>
                        {supportedRes?.map((res, index) => (
                          <option key={index} value={`${res.width}x${res.height}`}>
                            {res.label || `${res.width}x${res.height}`}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Stream Type */}
                    <div className="form-group">
                      <label htmlFor="stream-type-select">Stream Type</label>
                      <select
                        id="stream-type-select"
                        className="form-control"
                        value={selectedStreamType}
                        onChange={(e) => setSelectedStreamType(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Stream Type --</option>
                        {supportedStreamSettings?.streamTypes?.map((type, index) => (
                          <option key={index} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Video Type */}
                    <div className="form-group">
                      <label htmlFor="video-type-select">Video Type</label>
                      <select
                        id="video-type-select"
                        className="form-control"
                        value={selectedVideoType}
                        onChange={(e) => setSelectedVideoType(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Video Type --</option>
                        {supportedStreamSettings?.videoTypes?.map((type, index) => (
                          <option key={index} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Bitrate Type */}
                    <div className="form-group">
                      <label htmlFor="bitrate-type-select">Bitrate Type</label>
                      <select
                        id="bitrate-type-select"
                        className="form-control"
                        value={selectedBitrateType}
                        onChange={(e) => setSelectedBitrateType(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Bitrate Type --</option>
                        {supportedStreamSettings?.bitrateTypes?.map((type, index) => (
                          <option key={index} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Video Quality */}
                    <div className="form-group">
                      <label htmlFor="video-quality-select">Video Quality</label>
                      <select
                        id="video-quality-select"
                        className="form-control"
                        value={selectedVideoQuality}
                        onChange={(e) => setSelectedVideoQuality(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Video Quality --</option>
                        {supportedStreamSettings?.videoQualities?.map((quality, index) => (
                          <option key={index} value={quality.value}>
                            {quality.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Frame Rate */}
                    <div className="form-group">
                      <label htmlFor="frame-rate-select">Frame Rate</label>
                      <select
                        id="frame-rate-select"
                        className="form-control"
                        value={selectedFrameRate}
                        onChange={(e) => {
                          const value = e.target.value;
                          // Handle both numeric and string frame rates (like "1/16", "1/8", etc.)
                          if (value.includes('/') || isNaN(value)) {
                            setSelectedFrameRate(value);
                          } else {
                            setSelectedFrameRate(parseInt(value));
                          }
                        }}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Frame Rate --</option>
                        {supportedStreamSettings?.frameRates?.map((rate, index) => (
                          <option key={index} value={rate.value}>
                            {rate.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Max Bitrate */}
                    <div className="form-group">
                      <label htmlFor="max-bitrate-select">Max Bitrate</label>
                      <select
                        id="max-bitrate-select"
                        className="form-control"
                        value={selectedMaxBitrate}
                        onChange={(e) => setSelectedMaxBitrate(parseInt(e.target.value))}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Max Bitrate --</option>
                        {supportedStreamSettings?.maxBitrates?.map((bitrate, index) => (
                          <option key={index} value={bitrate.value}>
                            {bitrate.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Video Encoding */}
                    <div className="form-group">
                      <label htmlFor="video-encoding-select">Video Encoding</label>
                      <select
                        id="video-encoding-select"
                        className="form-control"
                        value={selectedVideoEncoding}
                        onChange={(e) => setSelectedVideoEncoding(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Video Encoding --</option>
                        {supportedStreamSettings?.videoEncodings?.map((encoding, index) => (
                          <option key={index} value={encoding.value}>
                            {encoding.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* H.265+ */}
                    <div className="form-group">
                      <label htmlFor="h265-plus-select">H.265+</label>
                      <select
                        id="h265-plus-select"
                        className="form-control"
                        value={selectedH265Plus}
                        onChange={(e) => setSelectedH265Plus(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select H.265+ --</option>
                        {supportedStreamSettings?.h265PlusOptions?.map((option, index) => (
                          <option key={index} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Profile */}
                    <div className="form-group">
                      <label htmlFor="profile-select">Profile</label>
                      <select
                        id="profile-select"
                        className="form-control"
                        value={selectedProfile}
                        onChange={(e) => setSelectedProfile(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select Profile --</option>
                        {supportedStreamSettings?.profiles?.map((profile, index) => (
                          <option key={index} value={profile.value}>
                            {profile.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* I Frame Interval */}
                    <div className="form-group">
                      <label htmlFor="i-frame-interval">I Frame Interval</label>
                      <input
                        id="i-frame-interval"
                        type="number"
                        className="form-control"
                        value={selectedIFrameInterval}
                        onChange={(e) => setSelectedIFrameInterval(parseInt(e.target.value))}
                        min="1"
                        max="100"
                        disabled={isLoading}
                      />
                    </div>

                    {/* SVC */}
                    <div className="form-group">
                      <label htmlFor="svc-select">SVC</label>
                      <select
                        id="svc-select"
                        className="form-control"
                        value={selectedSVC}
                        onChange={(e) => setSelectedSVC(e.target.value)}
                        disabled={isLoading}
                      >
                        <option value="">-- Select SVC --</option>
                        {supportedStreamSettings?.svcOptions?.map((option, index) => (
                          <option key={index} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Smoothing */}
                    <div className="form-group">
                      <label htmlFor="smoothing-slider">Smoothing: {selectedSmoothing}</label>
                      <input
                        id="smoothing-slider"
                        type="range"
                        className="form-control"
                        value={selectedSmoothing}
                        onChange={(e) => setSelectedSmoothing(parseInt(e.target.value))}
                        min="0"
                        max="100"
                        step="1"
                        disabled={isLoading}
                      />
                      <div className="slider-labels">
                        <span>Clear</span>
                        <span>Smooth</span>
                      </div>
                    </div>
                  </div>

                  <button
                    className="btn btn-success"
                    onClick={applyVideoSettings}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Applying...' : 'Apply Video Settings'}
                  </button>
                </div>
              </div>
            )}
          </>
        )}

        {error && (
          <div className="alert alert-error">
            <strong>Error:</strong> {error}
          </div>
        )}

        {success && (
          <div className="alert alert-success">
            <strong>Success:</strong> {success}
          </div>
        )}
      </div>
    </div>
  );
};

export default CameraSettings;
