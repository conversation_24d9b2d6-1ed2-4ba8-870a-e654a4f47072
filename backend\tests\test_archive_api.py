import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock
from fastapi.testclient import TestClient
from fastapi import FastAPI

from routes.archive import router, set_archive_manager
from archive_manager import ArchiveRecordingManager


@pytest.fixture
def temp_recordings_dir():
    """Create a temporary directory for test recordings"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def mock_archive_manager(temp_recordings_dir):
    """Create a mock archive manager for testing"""
    manager = Mock(spec=ArchiveRecordingManager)
    manager.recordings_base_path = temp_recordings_dir
    
    # Mock methods
    manager.get_available_recordings.return_value = [
        "2025-01-15_00-00-00.mp4",
        "2025-01-14_00-00-00.mp4"
    ]
    
    manager.get_recording_info.return_value = {
        "filename": "2025-01-15_00-00-00.mp4",
        "stream_id": "Eagle_192.168.4.243",
        "timestamp": "2025-01-15T00:00:00",
        "size_bytes": 1048576,
        "size_mb": 1.0,
        "path": str(temp_recordings_dir / "Eagle_192.168.4.243" / "2025-01-15_00-00-00.mp4")
    }
    
    manager.get_recording_path.return_value = temp_recordings_dir / "Eagle_192.168.4.243" / "2025-01-15_00-00-00.mp4"
    
    manager.get_status.return_value = {
        "active_recordings": 2,
        "recording_threads": 2,
        "stream_ids": ["Eagle_192.168.4.243", "Eagle_192.168.4.242"],
        "recordings_path": str(temp_recordings_dir),
        "cleanup_active": True
    }
    
    return manager


@pytest.fixture
def test_app(mock_archive_manager):
    """Create a test FastAPI app with archive router"""
    app = FastAPI()
    app.include_router(router)
    
    # Set the mock archive manager
    set_archive_manager(mock_archive_manager)
    
    return app


@pytest.fixture
def client(test_app):
    """Create a test client"""
    return TestClient(test_app)


class TestArchiveAPI:
    """Test cases for archive API endpoints"""
    
    def test_list_recordings_success(self, client, mock_archive_manager):
        """Test successful recording list retrieval"""
        response = client.get("/api/archive/list/Eagle_192.168.4.243")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["stream_id"] == "Eagle_192.168.4.243"
        assert "recordings" in data
        assert "count" in data
        
        # Verify manager methods were called
        mock_archive_manager.get_available_recordings.assert_called_once_with("Eagle_192.168.4.243")
    
    def test_list_recordings_invalid_stream_id(self, client):
        """Test recording list with invalid stream ID"""
        response = client.get("/api/archive/list/invalid_stream")
        
        assert response.status_code == 400
        assert "Invalid stream ID format" in response.json()["detail"]
    
    def test_list_recordings_no_archive_manager(self, test_app):
        """Test recording list when archive manager is not available"""
        # Clear the archive manager
        set_archive_manager(None)
        
        client = TestClient(test_app)
        response = client.get("/api/archive/list/Eagle_192.168.4.243")
        
        assert response.status_code == 503
        assert "Archive recording service not available" in response.json()["detail"]
    
    def test_stream_recording_success(self, client, mock_archive_manager, temp_recordings_dir):
        """Test successful recording streaming"""
        # Create a test video file
        stream_dir = temp_recordings_dir / "Eagle_192.168.4.243"
        stream_dir.mkdir(parents=True)
        test_file = stream_dir / "2025-01-15_00-00-00.mp4"
        test_file.write_bytes(b"fake video content")
        
        # Mock the recording path to return the test file
        mock_archive_manager.get_recording_path.return_value = test_file
        
        response = client.get("/api/archive/stream/Eagle_192.168.4.243/2025-01-15_00-00-00.mp4")
        
        assert response.status_code == 200
        assert response.headers["content-type"] == "video/mp4"
        assert "Content-Length" in response.headers
        
        # Verify manager method was called
        mock_archive_manager.get_recording_path.assert_called_once_with(
            "Eagle_192.168.4.243", "2025-01-15_00-00-00.mp4"
        )
    
    def test_stream_recording_not_found(self, client, mock_archive_manager):
        """Test streaming non-existent recording"""
        mock_archive_manager.get_recording_path.return_value = None
        
        response = client.get("/api/archive/stream/Eagle_192.168.4.243/nonexistent.mp4")
        
        assert response.status_code == 404
        assert "Recording file not found" in response.json()["detail"]
    
    def test_stream_recording_invalid_format(self, client):
        """Test streaming with invalid file format"""
        response = client.get("/api/archive/stream/Eagle_192.168.4.243/invalid.txt")
        
        assert response.status_code == 400
        assert "Invalid file format" in response.json()["detail"]
    
    def test_get_archive_status_success(self, client, mock_archive_manager):
        """Test successful archive status retrieval"""
        response = client.get("/api/archive/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "active"
        assert data["active_recordings"] == 2
        assert data["recording_threads"] == 2
        assert "stream_ids" in data
        
        # Verify manager method was called
        mock_archive_manager.get_status.assert_called_once()
    
    def test_get_archive_status_no_manager(self, test_app):
        """Test archive status when manager is not available"""
        set_archive_manager(None)
        
        client = TestClient(test_app)
        response = client.get("/api/archive/status")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "unavailable"
        assert "not initialized" in data["message"]
    
    def test_list_available_streams_success(self, client, mock_archive_manager, temp_recordings_dir):
        """Test successful available streams listing"""
        # Create test stream directories
        (temp_recordings_dir / "Eagle_192.168.4.243").mkdir(parents=True)
        (temp_recordings_dir / "Eagle_192.168.4.242").mkdir(parents=True)
        
        # Mock available recordings for each stream
        def mock_get_recordings(stream_id):
            if stream_id == "Eagle_192.168.4.243":
                return ["2025-01-15_00-00-00.mp4", "2025-01-14_00-00-00.mp4"]
            elif stream_id == "Eagle_192.168.4.242":
                return ["2025-01-15_00-00-00.mp4"]
            return []
        
        mock_archive_manager.get_available_recordings.side_effect = mock_get_recordings
        
        response = client.get("/api/archive/streams")
        
        assert response.status_code == 200
        data = response.json()
        
        assert "streams" in data
        assert "count" in data
        assert len(data["streams"]) >= 0  # Depends on mock implementation
    
    def test_delete_recording_success(self, client, mock_archive_manager, temp_recordings_dir):
        """Test successful recording deletion"""
        # Create a test file to delete
        stream_dir = temp_recordings_dir / "Eagle_192.168.4.243"
        stream_dir.mkdir(parents=True)
        test_file = stream_dir / "2025-01-15_00-00-00.mp4"
        test_file.write_bytes(b"fake video content")
        
        # Mock the recording path to return the test file
        mock_archive_manager.get_recording_path.return_value = test_file
        
        response = client.delete("/api/archive/recordings/Eagle_192.168.4.243/2025-01-15_00-00-00.mp4")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["status"] == "success"
        assert "deleted successfully" in data["message"]
        
        # Verify file was deleted
        assert not test_file.exists()
    
    def test_delete_recording_not_found(self, client, mock_archive_manager):
        """Test deleting non-existent recording"""
        mock_archive_manager.get_recording_path.return_value = None
        
        response = client.delete("/api/archive/recordings/Eagle_192.168.4.243/nonexistent.mp4")
        
        assert response.status_code == 404
        assert "Recording file not found" in response.json()["detail"]
    
    def test_delete_recording_invalid_format(self, client):
        """Test deleting with invalid file format"""
        response = client.delete("/api/archive/recordings/Eagle_192.168.4.243/invalid.txt")
        
        assert response.status_code == 400
        assert "Invalid file format" in response.json()["detail"]


class TestArchiveAPIValidation:
    """Test input validation for archive API"""
    
    def test_invalid_stream_id_formats(self, client):
        """Test various invalid stream ID formats"""
        invalid_ids = ["", "no_underscore", "multiple_underscores_but_invalid"]
        
        for stream_id in invalid_ids:
            response = client.get(f"/api/archive/list/{stream_id}")
            assert response.status_code == 400
    
    def test_invalid_filenames(self, client):
        """Test various invalid filename formats"""
        invalid_files = ["", "no_extension", "wrong.txt", "invalid.avi"]
        
        for filename in invalid_files:
            response = client.get(f"/api/archive/stream/Eagle_192.168.4.243/{filename}")
            assert response.status_code == 400


if __name__ == "__main__":
    pytest.main([__file__])
