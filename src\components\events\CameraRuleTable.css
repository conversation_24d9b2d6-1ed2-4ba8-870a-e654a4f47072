.camera-rule-table {
  background-color: #1a1a1a;
  border-radius: 8px;
  overflow: hidden;
  margin-top: 20px;
  color: #f0f0f0;
  padding: 20px;
}

.zone-selector-container {
  margin-bottom: 30px;
}

.zone-selector-container h3 {
  font-size: 20px;
  margin-bottom: 15px;
  color: #fff;
}

.zone-dropdown {
  max-width: 100%;
  width: 100%;
}

.zone-select {
  width: 100%;
  padding: 12px 16px;
  border-radius: 8px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #f0f0f0;
  font-size: 16px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f0f0f0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
}

/* Zone cameras container */
.zone-cameras-container {
  margin-top: 20px;
}

.zone-cameras-container h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #b6e14b;
}

/* Camera rules list */
.camera-rules-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.camera-rule-item {
  background-color: #2c2c2c;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.camera-name {
  display: flex;
  align-items: center;
}

.camera-name span {
  font-size: 16px;
  font-weight: 600;
  color: #a663d0;
}

.applied-rules {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.applied-rules h4 {
  font-size: 14px;
  color: #aaa;
  margin: 0;
}

/* Detection checkboxes */
.detection-checkboxes {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 5px;
}

.detection-checkbox-container {
  position: relative;
}

.detection-checkbox-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.detection-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #b6e14b;
  margin-bottom: 5px;
}

.detection-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background-color: #3a3a3a;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 600;
  color: #f0f0f0;
  transition: all 0.2s;
}

.detection-checkbox:checked + .detection-number {
  background-color: #b6e14b;
  color: #1a1a1a;
}

/* Tooltip */
.detection-tooltip {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  pointer-events: none;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  transform: translateY(5px);
}

.detection-tooltip::before {
  content: '';
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 5px 5px;
  border-style: solid;
  border-color: transparent transparent rgba(0, 0, 0, 0.8);
}

.no-cameras {
  text-align: center;
  padding: 30px;
  color: #888;
  font-style: italic;
  background-color: #2c2c2c;
  border-radius: 8px;
}
