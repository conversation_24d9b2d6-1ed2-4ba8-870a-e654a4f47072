// React must be in scope when using JSX
import React, { useState, useEffect } from 'react';
import { useCameraStore } from '../../store/cameraStore';
import { useArchiveStore } from '../../store/archiveStore';
import FilterCard from '../events/FilterCard';
import archiveApi from '../../services/archiveApi';
import VideoPlayer from './VideoPlayer';
import RecordingStatusIndicator, { OverallRecordingStatus } from './RecordingStatusIndicator';
import './ArchivePlaybackContent.css';

// Helper function to format date
const formatDate = (timestamp) => {
  if (!timestamp) return 'Unknown';

  try {
    const date = new Date(timestamp);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  } catch (error) {
    return 'Invalid Date';
  }
};

const ArchivePlaybackContent = ({ onSelectRecording, selectedRecordingId }) => {
  const { cameras } = useCameraStore();
  const {
    recordings,
    availableStreams,
    isLoading,
    error,
    filters,
    selectedStreamId,
    loadRecordings,
    loadAvailableStreams,
    setFilters,
    getFilteredRecordings,
    clearError,
    startStatusPolling,
    stopStatusPolling,
    restartRecordings
  } = useArchiveStore();

  const [filteredRecordings, setFilteredRecordings] = useState([]);

  // Load available streams and start status polling on component mount
  useEffect(() => {
    // Always load available streams first to show existing recordings
    loadAvailableStreams();

    // Start status polling for real-time recording status updates
    startStatusPolling();

    // Cleanup on unmount
    return () => {
      stopStatusPolling();
    };
  }, [loadAvailableStreams, startStatusPolling, stopStatusPolling]);

  // Update filtered recordings when recordings or filters change
  useEffect(() => {
    const filtered = getFilteredRecordings();
    setFilteredRecordings(filtered);
  }, [recordings, filters, getFilteredRecordings]);

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    setFilters({ [filterType]: value });

    // If camera filter changed, load recordings for that camera
    if (filterType === 'camera' && value !== 'all') {
      const camera = cameras.find(cam => cam.id === value);
      if (camera && camera.collection && camera.ip) {
        loadRecordings(`${camera.collection}_${camera.ip}`);
      }
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    clearError();
    if (selectedStreamId) {
      await loadRecordings(selectedStreamId);
    } else {
      await loadAvailableStreams();
    }
  };

  // Handle restart recordings
  const handleRestartRecordings = async () => {
    try {
      await restartRecordings();
      // Refresh data after restart
      await handleRefresh();
    } catch (error) {
      console.error('Failed to restart recordings:', error);
    }
  };

  // Get camera options for filter
  const getCameraOptions = () => {
    const options = [{ value: 'all', label: 'All Cameras' }];

    // Add cameras from available streams
    availableStreams.forEach(stream => {
      options.push({
        value: stream.stream_id,
        label: `${stream.collection_name} (${stream.camera_ip})`
      });
    });

    return options;
  };

  // Handle recording selection for playback
  const handlePlayRecording = (recording) => {
    console.log('=== RECORDING SELECTION DEBUG ===');
    console.log('Recording object:', recording);
    console.log('Stream ID:', recording.stream_id);
    console.log('Filename:', recording.filename);

    if (recording && recording.stream_id && recording.filename) {
      const streamUrl = archiveApi.getRecordingStreamUrl(recording.stream_id, recording.filename);
      console.log('Generated stream URL:', streamUrl);

      const recordingData = {
        id: recording.filename,
        streamId: recording.stream_id,
        filename: recording.filename,
        streamUrl: streamUrl,
        timestamp: recording.timestamp,
        size: recording.size_mb
      };

      console.log('Recording data passed to VideoPlayer:', recordingData);
      onSelectRecording(recordingData);
    } else {
      console.error('Invalid recording data:', {
        hasRecording: !!recording,
        hasStreamId: !!recording?.stream_id,
        hasFilename: !!recording?.filename
      });
    }
  };

  return (
    <div className="archive-playback-content">
      {selectedRecordingId ? (
        // Show video player for selected recording
        <div className="recording-player-container">
          <div className="player-header">
            <button
              className="back-button"
              onClick={() => onSelectRecording(null)}
              aria-label="Back to recordings list"
            >
              ← Back to Recordings
            </button>
            <div className="recording-details">
              <h3>Archive Playback</h3>
              {selectedRecordingId.timestamp && (
                <p className="recording-timestamp">
                  {formatDate(selectedRecordingId.timestamp)}
                </p>
              )}
              {selectedRecordingId.size && (
                <p className="recording-size">
                  Size: {archiveApi.formatFileSize(selectedRecordingId.size * 1024 * 1024)}
                </p>
              )}
              <p className="recording-duration">
                Duration: {archiveApi.formatRecordingDuration(selectedRecordingId)}
              </p>
            </div>
          </div>

          <div className="video-player-wrapper">
            {selectedRecordingId.streamUrl ? (
              <VideoPlayer
                key={selectedRecordingId.streamUrl}
                src={selectedRecordingId.streamUrl}
                recording={selectedRecordingId}
                onError={(error) => {
                  console.error('Video playback error:', error);
                  // Could add error state management here if needed
                }}
              />
            ) : (
              <div className="video-error">
                <p>Unable to load recording. Please try again.</p>
              </div>
            )}
          </div>
        </div>
      ) : (
        // Show the recordings list when no recording is selected
        <div className="recordings-container">
          <div className="archive-filters-container">
            <div className="archive-filters-header">
              <div className="archive-title">
                <h2>Archive Playback</h2>
                <OverallRecordingStatus />
              </div>
              <div className="action-buttons">
                <button
                  className="refresh-button"
                  onClick={handleRefresh}
                  aria-label="Refresh recordings"
                  disabled={isLoading}
                >
                  <span className="refresh-icon">⟳</span>
                  Refresh
                </button>
                <button
                  className="restart-button"
                  onClick={handleRestartRecordings}
                  aria-label="Restart failed recordings"
                  disabled={isLoading}
                  title="Restart any failed recording processes"
                >
                  <span className="restart-icon">🔄</span>
                  Restart Recordings
                </button>
              </div>
            </div>

            <div className="archive-filters-grid">
              <FilterCard
                title="Date Range"
                type="select"
                value={filters.dateRange}
                onChange={(value) => handleFilterChange('dateRange', value)}
                options={[
                  { value: 'today', label: 'Today' },
                  { value: 'yesterday', label: 'Yesterday' },
                  { value: 'week', label: 'This Week' },
                  { value: 'month', label: 'This Month' },
                  { value: 'all', label: 'All Time' }
                ]}
                isActive={filters.dateRange !== 'today'}
              />

              <FilterCard
                title="Camera"
                type="select"
                value={filters.streamId || 'all'}
                onChange={(value) => handleFilterChange('streamId', value)}
                options={getCameraOptions()}
                isActive={filters.streamId !== 'all'}
              />

              <FilterCard
                title="Sort By"
                type="select"
                value={filters.sortBy}
                onChange={(value) => handleFilterChange('sortBy', value)}
                options={[
                  { value: 'newest', label: 'Newest First' },
                  { value: 'oldest', label: 'Oldest First' },
                  { value: 'largest', label: 'Largest First' },
                  { value: 'smallest', label: 'Smallest First' }
                ]}
                isActive={filters.sortBy !== 'newest'}
              />
            </div>
          </div>



          {/* Loading state */}
          {isLoading && (
            <div className="loading-container">
              <div className="loading-spinner"></div>
              <p>Loading recordings...</p>
            </div>
          )}

          {/* Error state */}
          {error && (
            <div className="error-container">
              <div className="error-icon">⚠️</div>
              <p className="error-message">{error}</p>
              {error.includes('backend is not available') && (
                <div className="backend-unavailable-info">
                  <p className="info-text">
                    The archive backend is currently not running. To access recordings:
                  </p>
                  <ul className="info-list">
                    <li>Start the Python backend server</li>
                    <li>Ensure the archive recording service is running</li>
                    <li>Check that recordings exist in the ./recordings/ directory</li>
                  </ul>
                </div>
              )}
              <button className="retry-button" onClick={handleRefresh}>
                Try Again
              </button>
            </div>
          )}

          {/* Recordings grid */}
          {!isLoading && !error && (
            <div className="recordings-grid">
              {filteredRecordings.length === 0 ? (
                <div className="no-recordings">
                  <div className="no-recordings-icon">📁</div>
                  <p>No archived recordings found.</p>
                  <p className="hint">
                    {selectedStreamId
                      ? "No recordings available for the selected camera."
                      : "Select a camera to view its recordings."
                    }
                  </p>
                  {selectedStreamId && (
                    <button
                      className="clear-filter-button"
                      onClick={() => setFilters({ dateRange: 'all', sortBy: 'newest' })}
                    >
                      Show All Recordings
                    </button>
                  )}
                </div>
              ) : (
                filteredRecordings.map(recording => (
                  <div key={recording.filename} className="recording-card">
                    <div className="recording-thumbnail">
                      <div className="thumbnail-placeholder">
                        <span className="camera-icon">📹</span>
                      </div>
                    </div>
                    <div className="recording-info">
                      <div className="recording-camera">
                        <div className="camera-name-with-status">
                          <span className="camera-name">
                            {archiveApi.parseStreamId(recording.stream_id).collectionName}
                            ({archiveApi.parseStreamId(recording.stream_id).cameraIp})
                          </span>
                          <RecordingStatusIndicator
                            streamId={recording.stream_id}
                            showLabel={false}
                            size="small"
                          />
                        </div>
                      </div>
                      <div className="recording-time">
                        <span>{formatDate(recording.timestamp)}</span>
                      </div>
                      <div className="recording-duration">
                        <span>{archiveApi.formatRecordingDuration(recording)}</span>
                      </div>
                      <div className="recording-size">
                        {archiveApi.formatFileSize(recording.size_bytes)}
                      </div>
                    </div>
                    <div className="recording-actions">
                      <button
                        className="play-button"
                        title="Play Recording"
                        onClick={() => handlePlayRecording(recording)}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#FFFFFF">
                          <polygon points="5,3 19,12 5,21"></polygon>
                        </svg>
                        <span>Play</span>
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Use React.memo to optimize rendering
export default React.memo(ArchivePlaybackContent);
