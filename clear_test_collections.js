// <PERSON>ript to clear test_collection entries from localStorage
// This script should be run in the browser console

function clearTestCollections() {
  console.log("Starting cleanup of test_collection entries...");
  let changesMade = false;

  // Get the camera storage from localStorage
  const cameraStorageKey = 'camera-storage';
  const cameraStorage = localStorage.getItem(cameraStorageKey);

  // Check all localStorage keys for any that might contain test_collection
  console.log("Scanning all localStorage keys for test_collection references...");
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);

    if (value && value.toLowerCase().includes('test_collection')) {
      console.log(`Found test_collection reference in localStorage key: ${key}`);

      try {
        // Try to parse as JSON and clean if possible
        const parsed = JSON.parse(value);
        let modified = false;

        // Deep clean function to remove test_collection references
        function deepClean(obj) {
          if (!obj || typeof obj !== 'object') return false;

          let changed = false;

          // Handle arrays
          if (Array.isArray(obj)) {
            const originalLength = obj.length;
            // Filter out test_collection items
            for (let i = obj.length - 1; i >= 0; i--) {
              const item = obj[i];
              if (typeof item === 'object' && item !== null) {
                // Check if this object has a name property with test_collection
                if (item.name && typeof item.name === 'string' &&
                    item.name.toLowerCase().includes('test_collection')) {
                  console.log(`Removing array item with test_collection name: ${item.name}`);
                  obj.splice(i, 1);
                  changed = true;
                  continue;
                }

                // Recursively clean this object
                if (deepClean(item)) {
                  changed = true;
                }
              } else if (typeof item === 'string' && item.toLowerCase().includes('test_collection')) {
                console.log(`Removing string array item with test_collection: ${item}`);
                obj.splice(i, 1);
                changed = true;
              }
            }
            return changed;
          }

          // Handle objects
          for (const prop in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, prop)) {
              // Check if property name contains test_collection
              if (prop.toLowerCase().includes('test_collection')) {
                console.log(`Removing object property with test_collection key: ${prop}`);
                delete obj[prop];
                changed = true;
                continue;
              }

              // Check if property value is a string containing test_collection
              if (typeof obj[prop] === 'string' && obj[prop].toLowerCase().includes('test_collection')) {
                console.log(`Removing string property with test_collection value: ${obj[prop]}`);
                delete obj[prop];
                changed = true;
                continue;
              }

              // Recursively clean nested objects
              if (typeof obj[prop] === 'object' && obj[prop] !== null) {
                if (deepClean(obj[prop])) {
                  changed = true;
                }
              }
            }
          }

          return changed;
        }

        // Apply deep cleaning
        if (deepClean(parsed)) {
          localStorage.setItem(key, JSON.stringify(parsed));
          console.log(`Cleaned and updated localStorage key: ${key}`);
          changesMade = true;
          modified = true;
        }

        if (!modified) {
          console.log(`No test_collection references found in parsed object for key: ${key}`);
        }
      } catch (e) {
        console.log(`Could not parse as JSON: ${key}. Skipping.`);
      }
    }
  }

  if (cameraStorage) {
    try {
      const parsedStorage = JSON.parse(cameraStorage);
      console.log("Current camera storage state:", parsedStorage);

      // Check if there are collections
      if (parsedStorage.state && parsedStorage.state.collections) {
        // Filter out any collections with "test_collection" in the name
        const filteredCollections = parsedStorage.state.collections.filter(collection => {
          const isTestCollection = collection.name && collection.name.toLowerCase().includes('test_collection');
          if (isTestCollection) {
            console.log(`Removing collection: ${collection.name}`);
            changesMade = true;
          }
          return !isTestCollection;
        });

        // Update the collections array
        parsedStorage.state.collections = filteredCollections;

        // Also check cameras array and remove any associated with test_collection
        if (parsedStorage.state.cameras) {
          const filteredCameras = parsedStorage.state.cameras.filter(camera => {
            // Check if camera name contains test_collection or if it has specific IPs
            const isTestCamera =
              (camera.name && camera.name.toLowerCase().includes('test_collection')) ||
              (camera.ip === '************' || camera.ip === '************') ||
              (camera.streamUrl && camera.streamUrl.includes('************')) ||
              (camera.streamUrl && camera.streamUrl.includes('************')) ||
              (camera.id && camera.id.toLowerCase().includes('test_collection')) ||
              (camera.collectionId && parsedStorage.state.collections.some(
                c => c.id === camera.collectionId && c.name && c.name.toLowerCase().includes('test_collection')
              ));

            if (isTestCamera) {
              console.log(`Removing camera: ${camera.name || camera.ip || camera.id || 'with IP in stream URL'}`);
              changesMade = true;
            }
            return !isTestCamera;
          });

          parsedStorage.state.cameras = filteredCameras;
        }

        // Check for any activeCollection reference to test_collection
        if (parsedStorage.state.activeCollection) {
          const activeCollectionId = parsedStorage.state.activeCollection;
          const activeCollection = parsedStorage.state.collections.find(c => c.id === activeCollectionId);

          if (activeCollection && activeCollection.name &&
              activeCollection.name.toLowerCase().includes('test_collection')) {
            console.log(`Clearing active collection reference to test_collection: ${activeCollection.name}`);
            parsedStorage.state.activeCollection = null;
            changesMade = true;
          }
        }

        // Check and clean cameraJson if it exists
        if (parsedStorage.state.cameraJson) {
          const newCameraJson = { ...parsedStorage.state.cameraJson };

          // Remove any test_collection entries
          Object.keys(newCameraJson).forEach(key => {
            if (key.toLowerCase().includes('test_collection')) {
              console.log(`Removing cameraJson entry: ${key}`);
              delete newCameraJson[key];
            }
          });

          parsedStorage.state.cameraJson = newCameraJson;
        }

        // Save the updated storage back to localStorage
        localStorage.setItem(cameraStorageKey, JSON.stringify(parsedStorage));
        console.log("Updated camera storage state:", parsedStorage);

        console.log("Cleanup completed successfully!");
        changesMade = true;
      } else {
        console.log("No collections found in storage");
      }
    } catch (error) {
      console.error("Error processing localStorage:", error);
    }
  } else {
    console.log("No camera storage found in localStorage");
  }

  // Check for any active menu items in the DOM that might be showing test_collection
  console.log("Checking DOM for test_collection menu items...");
  try {
    const menuItems = document.querySelectorAll('.submenu-label span');
    menuItems.forEach(item => {
      if (item.textContent.toLowerCase().includes('test_collection')) {
        console.log(`Found test_collection in menu item: "${item.textContent}"`);
        console.log("Please refresh the page to remove this from the UI");
        changesMade = true;
      }
    });
  } catch (e) {
    console.log("Could not check DOM elements:", e);
  }

  if (changesMade) {
    console.log("Changes were made! Please refresh the application to see the updates.");
  } else {
    console.log("No test_collection references were found or removed.");
  }

  return changesMade;
}

// Execute the cleanup function
clearTestCollections();
