.rule-toolbar {
  background-color: #2c2c2c;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
}

.rule-toolbar-header {
  margin-bottom: 12px;
}

.rule-toolbar-header h3 {
  font-size: 18px;
  margin-bottom: 4px;
  color: #b6e14b;
}

.rule-toolbar-header p {
  font-size: 13px;
  color: #aaa;
  margin: 0;
}

.rule-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(50px, 1fr));
  gap: 10px;
  margin-bottom: 16px;
}

.rule-button {
  width: 50px;
  height: 50px;
  border-radius: 6px;
  border: none;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rule-button.enabled {
  background-color: #3a3a3a;
  color: #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.rule-button.enabled:hover {
  background-color: #4a4a4a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.rule-button.disabled {
  background-color: #2a2a2a;
  color: #666;
  cursor: not-allowed;
  opacity: 0.7;
}

.rule-button.selected {
  background-color: #b6e14b;
  color: #1a1a1a;
}

.rule-button.selected:hover {
  background-color: #c8f35c;
}

.selected-rules {
  margin-top: 16px;
  padding: 12px;
  background-color: #222;
  border-radius: 6px;
  display: flex;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-rules > span {
  font-weight: 600;
  color: #aaa;
  margin-right: 8px;
}

.no-rules-selected {
  color: #666;
  font-style: italic;
}

.selected-rule-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.rule-tag {
  background-color: #b6e14b;
  color: #1a1a1a;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.remove-rule {
  background: none;
  border: none;
  color: #1a1a1a;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  margin-left: 6px;
  padding: 0 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.remove-rule:hover {
  background-color: rgba(0, 0, 0, 0.2);
}
