import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import VideoGrid from '../VideoGrid';

// Mock react-dnd
jest.mock('react-dnd', () => ({
  useDrag: () => [{ isDragging: false }, jest.fn(), jest.fn()],
  useDrop: () => [{ isOver: false, canDrop: true }, jest.fn()],
  DndProvider: ({ children }) => children,
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}));

// Mock the DraggableVideoGridCell component
jest.mock('../DraggableVideoGridCell', () => {
  return function MockDraggableVideoGridCell({ camera, index, onSwap, onCameraClick, isPlaceholder }) {
    return (
      <div
        data-testid={`grid-cell-${index}`}
        data-camera-id={camera?.id || 'placeholder'}
        data-is-placeholder={isPlaceholder}
        onClick={() => onCameraClick && camera && onCameraClick(camera)}
      >
        <button
          data-testid={`swap-button-${index}`}
          onClick={() => onSwap && onSwap(index, index + 1)}
        >
          Swap
        </button>
        {camera ? camera.name : 'Empty Slot'}
      </div>
    );
  };
});

// Mock the camera store
const mockSwapCameras = jest.fn();
jest.mock('../../../store/cameraStore', () => ({
  useCameraStore: () => ({
    swapCameras: mockSwapCameras,
  }),
}));

// Mock layout config
jest.mock('../../layout/LayoutModes', () => ({
  getLayoutConfig: (layout) => {
    const configs = {
      '1x1': { cols: 1, rows: 1, cameraCount: 1 },
      '2x2': { cols: 2, rows: 2, cameraCount: 4 },
      '3x3': { cols: 3, rows: 3, cameraCount: 9 },
      '4x4': { cols: 4, rows: 4, cameraCount: 16 },
    };
    return configs[layout] || { cols: 2, rows: 2, cameraCount: 4 };
  },
}));

const mockCameras = [
  {
    id: 'camera-1',
    name: 'Camera 1',
    streamUrl: 'http://test.com/stream1',
    ip: '*************',
  },
  {
    id: 'camera-2',
    name: 'Camera 2',
    streamUrl: 'http://test.com/stream2',
    ip: '*************',
  },
  {
    id: 'camera-3',
    name: 'Camera 3',
    streamUrl: 'http://test.com/stream3',
    ip: '*************',
  },
];

const renderWithDnd = (component) => {
  return render(component);
};

describe('VideoGrid', () => {
  const defaultProps = {
    layout: '2x2',
    cameras: mockCameras,
    cellCount: 4,
    onCameraClick: jest.fn(),
    activeTab: 'dashboard',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders video grid with correct structure', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      expect(screen.getByRole('grid')).toBeInTheDocument();
      expect(screen.getByLabelText(/Video grid with 3 cameras in 2x2 layout/)).toBeInTheDocument();
    });

    it('renders correct number of cells based on cellCount', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      // Should render 4 cells (cellCount = 4)
      expect(screen.getByTestId('grid-cell-0')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-1')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-2')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-3')).toBeInTheDocument();
    });

    it('renders cameras in correct positions', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      expect(screen.getByTestId('grid-cell-0')).toHaveAttribute('data-camera-id', 'camera-1');
      expect(screen.getByTestId('grid-cell-1')).toHaveAttribute('data-camera-id', 'camera-2');
      expect(screen.getByTestId('grid-cell-2')).toHaveAttribute('data-camera-id', 'camera-3');
      expect(screen.getByTestId('grid-cell-3')).toHaveAttribute('data-camera-id', 'placeholder');
    });

    it('renders placeholder cells for empty positions', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      expect(screen.getByTestId('grid-cell-3')).toHaveAttribute('data-is-placeholder', 'true');
      expect(screen.getByText('Empty Slot')).toBeInTheDocument();
    });

    it('applies correct grid layout styles', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      const gridElement = screen.getByRole('presentation');
      expect(gridElement).toHaveStyle({
        gridTemplateColumns: 'repeat(2, 1fr)',
        gridTemplateRows: 'repeat(2, 1fr)',
      });
    });
  });

  describe('Layout Configurations', () => {
    it('renders 1x1 layout correctly', () => {
      renderWithDnd(
        <VideoGrid
          {...defaultProps}
          layout="1x1"
          cellCount={1}
        />
      );

      const gridElement = screen.getByRole('presentation');
      expect(gridElement).toHaveStyle({
        gridTemplateColumns: 'repeat(1, 1fr)',
        gridTemplateRows: 'repeat(1, 1fr)',
      });
    });

    it('renders 3x3 layout correctly', () => {
      renderWithDnd(
        <VideoGrid
          {...defaultProps}
          layout="3x3"
          cellCount={9}
        />
      );

      const gridElement = screen.getByRole('presentation');
      expect(gridElement).toHaveStyle({
        gridTemplateColumns: 'repeat(3, 1fr)',
        gridTemplateRows: 'repeat(3, 1fr)',
      });
    });

    it('applies correct CSS classes for different layouts', () => {
      const { rerender } = renderWithDnd(<VideoGrid {...defaultProps} layout="1x1" />);
      expect(screen.getByRole('presentation')).toHaveClass('video-grid layout-1x1');

      rerender(<VideoGrid {...defaultProps} layout="2x2" />);
      expect(screen.getByRole('presentation')).toHaveClass('video-grid symmetrical');

      rerender(<VideoGrid {...defaultProps} layout="focus" />);
      expect(screen.getByRole('presentation')).toHaveClass('video-grid focus');
    });
  });

  describe('Camera Swapping', () => {
    it('handles swap between two cameras', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      // Simulate swapping camera at index 0 with camera at index 1
      fireEvent.click(screen.getByTestId('swap-button-0'));

      expect(mockSwapCameras).toHaveBeenCalledWith(0, 1);
    });

    it('does not swap when indices are invalid', () => {
      const handleSwap = jest.fn();

      // We need to test the handleSwap function directly since it's internal
      // This would be done through integration testing in a real scenario
      renderWithDnd(<VideoGrid {...defaultProps} />);

      // The swap validation is handled internally, so we test through the mock
      expect(mockSwapCameras).not.toHaveBeenCalledWith(-1, 0);
      expect(mockSwapCameras).not.toHaveBeenCalledWith(0, -1);
      expect(mockSwapCameras).not.toHaveBeenCalledWith(0, 0);
    });

    it('only swaps when both positions have cameras', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      // Try to swap camera with placeholder (should not call swapCameras)
      // This is tested through the component's internal logic
      expect(mockSwapCameras).not.toHaveBeenCalled();
    });
  });

  describe('Camera Interaction', () => {
    it('calls onCameraClick when camera is clicked', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      fireEvent.click(screen.getByTestId('grid-cell-0'));
      expect(defaultProps.onCameraClick).toHaveBeenCalledWith(mockCameras[0]);
    });

    it('does not call onCameraClick for placeholder cells', () => {
      renderWithDnd(<VideoGrid {...defaultProps} />);

      fireEvent.click(screen.getByTestId('grid-cell-3'));
      expect(defaultProps.onCameraClick).not.toHaveBeenCalled();
    });
  });

  describe('Re-rendering', () => {
    it('updates grid key when layout changes', () => {
      const { rerender } = renderWithDnd(<VideoGrid {...defaultProps} />);

      const initialGrid = screen.getByRole('presentation');
      const initialKey = initialGrid.getAttribute('key') || initialGrid.dataset.key;

      rerender(<VideoGrid {...defaultProps} layout="3x3" />);

      const updatedGrid = screen.getByRole('presentation');
      const updatedKey = updatedGrid.getAttribute('key') || updatedGrid.dataset.key;

      // The key should be different after layout change
      expect(updatedKey).not.toBe(initialKey);
    });

    it('updates grid key when activeTab changes', () => {
      const { rerender } = renderWithDnd(<VideoGrid {...defaultProps} />);

      rerender(<VideoGrid {...defaultProps} activeTab="bookmark" />);

      // Component should re-render with new activeTab
      expect(screen.getByRole('grid')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty cameras array', () => {
      renderWithDnd(
        <VideoGrid
          {...defaultProps}
          cameras={[]}
        />
      );

      // All cells should be placeholders
      expect(screen.getByTestId('grid-cell-0')).toHaveAttribute('data-is-placeholder', 'true');
      expect(screen.getByTestId('grid-cell-1')).toHaveAttribute('data-is-placeholder', 'true');
      expect(screen.getByTestId('grid-cell-2')).toHaveAttribute('data-is-placeholder', 'true');
      expect(screen.getByTestId('grid-cell-3')).toHaveAttribute('data-is-placeholder', 'true');
    });

    it('handles more cameras than cell count', () => {
      const manyCameras = [...mockCameras,
        { id: 'camera-4', name: 'Camera 4', streamUrl: 'http://test.com/stream4', ip: '*************' },
        { id: 'camera-5', name: 'Camera 5', streamUrl: 'http://test.com/stream5', ip: '*************' }
      ];

      renderWithDnd(
        <VideoGrid
          {...defaultProps}
          cameras={manyCameras}
          cellCount={4}
        />
      );

      // Should only render cellCount number of cells
      expect(screen.getByTestId('grid-cell-0')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-1')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-2')).toBeInTheDocument();
      expect(screen.getByTestId('grid-cell-3')).toBeInTheDocument();
      expect(screen.queryByTestId('grid-cell-4')).not.toBeInTheDocument();
    });
  });
});
