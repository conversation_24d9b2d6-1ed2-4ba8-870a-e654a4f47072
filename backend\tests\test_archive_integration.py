import pytest
import tempfile
import shutil
import json
import time
from pathlib import Path
from unittest.mock import patch, Mock

from archive_manager import ArchiveRecordingManager


@pytest.fixture
def temp_config_file():
    """Create a temporary camera configuration file"""
    config_data = {
        "TestCollection": {
            "192.168.1.100": "rtsp://test:test@192.168.1.100:554/stream",
            "192.168.1.101": "rtsp://test:test@192.168.1.101:554/stream"
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config_data, f)
        temp_file = f.name
    
    yield temp_file
    
    # Cleanup
    Path(temp_file).unlink(missing_ok=True)


@pytest.fixture
def temp_recordings_dir():
    """Create a temporary directory for recordings"""
    temp_dir = tempfile.mkdtemp()
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


class TestArchiveRecordingManagerIntegration:
    """Integration tests for the Archive Recording Manager"""
    
    def test_manager_initialization(self, temp_config_file, temp_recordings_dir):
        """Test that the manager initializes correctly"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        assert manager.camera_config_path == temp_config_file
        assert manager.recordings_base_path == temp_recordings_dir
        assert manager.recordings_base_path.exists()
        assert manager.cleanup_thread is not None
        assert manager.cleanup_thread.is_alive()
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_camera_config_loading(self, temp_config_file, temp_recordings_dir):
        """Test loading camera configuration"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        config = manager._load_camera_config()
        
        assert "TestCollection" in config
        assert "192.168.1.100" in config["TestCollection"]
        assert "192.168.1.101" in config["TestCollection"]
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_stream_id_generation(self, temp_config_file, temp_recordings_dir):
        """Test stream ID generation"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        stream_id = manager._get_stream_id("TestCollection", "192.168.1.100")
        assert stream_id == "TestCollection_192.168.1.100"
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_recording_directory_creation(self, temp_config_file, temp_recordings_dir):
        """Test recording directory creation"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        stream_id = "TestCollection_192.168.1.100"
        stream_dir = manager._create_recording_directory(stream_id)
        
        assert stream_dir.exists()
        assert stream_dir.is_dir()
        assert stream_dir.name == stream_id
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_filename_generation(self, temp_config_file, temp_recordings_dir):
        """Test recording filename generation"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        filename = manager._get_current_filename()
        
        # Should match format YYYY-MM-DD_HH-MM-SS.mp4
        assert filename.endswith('.mp4')
        assert len(filename) == 23  # YYYY-MM-DD_HH-MM-SS.mp4
        assert filename[4] == '-'
        assert filename[7] == '-'
        assert filename[10] == '_'
        assert filename[13] == '-'
        assert filename[16] == '-'
        
        # Cleanup
        manager.stop_all_recordings()
    
    @patch('subprocess.Popen')
    def test_recording_process_start(self, mock_popen, temp_config_file, temp_recordings_dir):
        """Test starting recording processes (mocked)"""
        # Mock the subprocess
        mock_process = Mock()
        mock_process.wait.return_value = 0
        mock_process.returncode = 0
        mock_popen.return_value = mock_process
        
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        # Start recordings (this will be mocked)
        manager.start_all_recordings()
        
        # Give it a moment to start threads
        time.sleep(0.1)
        
        # Should have started threads for each camera
        assert len(manager.recording_threads) == 2
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_available_recordings_empty(self, temp_config_file, temp_recordings_dir):
        """Test getting available recordings when none exist"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        recordings = manager.get_available_recordings("TestCollection_192.168.1.100")
        assert recordings == []
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_available_recordings_with_files(self, temp_config_file, temp_recordings_dir):
        """Test getting available recordings when files exist"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        # Create test recording files
        stream_id = "TestCollection_192.168.1.100"
        stream_dir = manager._create_recording_directory(stream_id)
        
        test_files = [
            "2025-01-15_00-00-00.mp4",
            "2025-01-14_00-00-00.mp4",
            "2025-01-13_00-00-00.mp4"
        ]
        
        for filename in test_files:
            (stream_dir / filename).write_bytes(b"fake video content")
        
        recordings = manager.get_available_recordings(stream_id)
        
        # Should return files sorted by newest first
        assert len(recordings) == 3
        assert recordings[0] == "2025-01-15_00-00-00.mp4"
        assert recordings[1] == "2025-01-14_00-00-00.mp4"
        assert recordings[2] == "2025-01-13_00-00-00.mp4"
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_recording_path_retrieval(self, temp_config_file, temp_recordings_dir):
        """Test getting recording file paths"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        # Create test recording file
        stream_id = "TestCollection_192.168.1.100"
        stream_dir = manager._create_recording_directory(stream_id)
        filename = "2025-01-15_00-00-00.mp4"
        test_file = stream_dir / filename
        test_file.write_bytes(b"fake video content")
        
        # Test existing file
        path = manager.get_recording_path(stream_id, filename)
        assert path == test_file
        assert path.exists()
        
        # Test non-existent file
        path = manager.get_recording_path(stream_id, "nonexistent.mp4")
        assert path is None
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_recording_info_retrieval(self, temp_config_file, temp_recordings_dir):
        """Test getting recording information"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        # Create test recording file
        stream_id = "TestCollection_192.168.1.100"
        stream_dir = manager._create_recording_directory(stream_id)
        filename = "2025-01-15_00-00-00.mp4"
        test_file = stream_dir / filename
        test_content = b"fake video content" * 1000  # Make it bigger
        test_file.write_bytes(test_content)
        
        info = manager.get_recording_info(stream_id, filename)
        
        assert info is not None
        assert info["filename"] == filename
        assert info["stream_id"] == stream_id
        assert info["timestamp"] == "2025-01-15T00:00:00"
        assert info["size_bytes"] == len(test_content)
        assert info["size_mb"] > 0
        assert info["path"] == str(test_file)
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_status_retrieval(self, temp_config_file, temp_recordings_dir):
        """Test getting manager status"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        status = manager.get_status()
        
        assert "active_recordings" in status
        assert "recording_threads" in status
        assert "stream_ids" in status
        assert "recordings_path" in status
        assert "cleanup_active" in status
        
        assert status["recordings_path"] == str(temp_recordings_dir)
        assert isinstance(status["cleanup_active"], bool)
        
        # Cleanup
        manager.stop_all_recordings()
    
    def test_cleanup_functionality(self, temp_config_file, temp_recordings_dir):
        """Test cleanup of old recordings"""
        manager = ArchiveRecordingManager(
            camera_config_path=temp_config_file,
            recordings_base_path=str(temp_recordings_dir)
        )
        
        # Create test files with old dates
        stream_id = "TestCollection_192.168.1.100"
        stream_dir = manager._create_recording_directory(stream_id)
        
        # Create files that should be cleaned up (older than 30 days)
        old_files = [
            "2024-01-01_00-00-00.mp4",  # Very old
            "2024-12-01_00-00-00.mp4"   # Old
        ]
        
        # Create files that should be kept (recent)
        recent_files = [
            "2025-01-15_00-00-00.mp4",
            "2025-01-14_00-00-00.mp4"
        ]
        
        for filename in old_files + recent_files:
            (stream_dir / filename).write_bytes(b"fake video content")
        
        # Run cleanup manually (normally runs in background)
        import datetime
        cutoff_date = datetime.datetime.now() - datetime.timedelta(days=30)
        
        for recording_file in stream_dir.glob("*.mp4"):
            try:
                filename = recording_file.stem
                file_date = datetime.datetime.strptime(filename, "%Y-%m-%d_%H-%M-%S")
                
                if file_date < cutoff_date:
                    recording_file.unlink()
            except (ValueError, OSError):
                pass
        
        # Check that old files were removed and recent files remain
        remaining_files = [f.name for f in stream_dir.glob("*.mp4")]
        
        for old_file in old_files:
            assert old_file not in remaining_files
        
        for recent_file in recent_files:
            assert recent_file in remaining_files
        
        # Cleanup
        manager.stop_all_recordings()


if __name__ == "__main__":
    pytest.main([__file__])
