/**
 * Integration tests for private IP validation in camera forms
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import AddCameraForm from '../../components/camera/AddCameraForm';
import { validatePrivateIP, extractIPFromStreamURL } from '../../utils/ipValidation';

// Mock the camera manager hook
jest.mock('../../components/camera/CameraManager', () => ({
  useCameras: () => ({
    addCamera: jest.fn(),
    updateCamera: jest.fn(),
    removeCamera: jest.fn(),
    collections: [{ id: 'test-collection', name: 'Test Collection' }],
    activeCollection: 'test-collection'
  })
}));

describe('Private IP Validation Integration', () => {
  describe('validatePrivateIP function', () => {
    it('should accept all private IP ranges', () => {
      // Class C private IPs
      expect(validatePrivateIP('*************').isValid).toBe(true);
      expect(validatePrivateIP('***********').isValid).toBe(true);
      expect(validatePrivateIP('***************').isValid).toBe(true);

      // Class A private IPs
      expect(validatePrivateIP('********').isValid).toBe(true);
      expect(validatePrivateIP('**************').isValid).toBe(true);

      // Class B private IPs
      expect(validatePrivateIP('**********').isValid).toBe(true);
      expect(validatePrivateIP('**************').isValid).toBe(true);
      expect(validatePrivateIP('************').isValid).toBe(true);
    });

    it('should reject public IP addresses', () => {
      expect(validatePrivateIP('*******').isValid).toBe(false);
      expect(validatePrivateIP('*******').isValid).toBe(false);
      expect(validatePrivateIP('**********').isValid).toBe(false); // Just outside Class B range
      expect(validatePrivateIP('**********').isValid).toBe(false); // Just outside Class B range
      expect(validatePrivateIP('***********').isValid).toBe(false); // Not 192.168
    });
  });

  describe('extractIPFromStreamURL function', () => {
    it('should extract IP addresses from various stream URL formats', () => {
      expect(extractIPFromStreamURL('rtsp://admin:password@*************:554/stream'))
        .toBe('*************');
      expect(extractIPFromStreamURL('http://*********:8080/video'))
        .toBe('*********');
      expect(extractIPFromStreamURL('rtsp://************:554/'))
        .toBe('************');
    });

    it('should return null for domain names', () => {
      expect(extractIPFromStreamURL('rtsp://camera.example.com:554/stream'))
        .toBeNull();
    });
  });

  describe('AddCameraForm IP validation', () => {
    it('should accept stream URLs with private IP addresses', async () => {
      render(<AddCameraForm />);
      
      const nameInput = screen.getByLabelText(/camera name/i);
      const urlInput = screen.getByLabelText(/stream url/i);
      const submitButton = screen.getByRole('button', { name: /add camera/i });

      fireEvent.change(nameInput, { target: { value: 'Test Camera' } });
      fireEvent.change(urlInput, { target: { value: 'rtsp://admin:password@*************:554/stream' } });
      fireEvent.click(submitButton);

      // Should not show any error for private IP
      await waitFor(() => {
        expect(screen.queryByText(/must be within private network ranges/i)).not.toBeInTheDocument();
      });
    });

    it('should reject stream URLs with public IP addresses', async () => {
      render(<AddCameraForm />);
      
      const nameInput = screen.getByLabelText(/camera name/i);
      const urlInput = screen.getByLabelText(/stream url/i);
      const submitButton = screen.getByRole('button', { name: /add camera/i });

      fireEvent.change(nameInput, { target: { value: 'Test Camera' } });
      fireEvent.change(urlInput, { target: { value: 'rtsp://admin:password@*******:554/stream' } });
      fireEvent.click(submitButton);

      // Should show error for public IP
      await waitFor(() => {
        expect(screen.getByText(/must be within private network ranges/i)).toBeInTheDocument();
      });
    });

    it('should allow stream URLs with domain names (no IP validation)', async () => {
      render(<AddCameraForm />);
      
      const nameInput = screen.getByLabelText(/camera name/i);
      const urlInput = screen.getByLabelText(/stream url/i);
      const submitButton = screen.getByRole('button', { name: /add camera/i });

      fireEvent.change(nameInput, { target: { value: 'Test Camera' } });
      fireEvent.change(urlInput, { target: { value: 'rtsp://camera.example.com:554/stream' } });
      fireEvent.click(submitButton);

      // Should not show IP validation error for domain names
      await waitFor(() => {
        expect(screen.queryByText(/must be within private network ranges/i)).not.toBeInTheDocument();
      });
    });
  });
});
