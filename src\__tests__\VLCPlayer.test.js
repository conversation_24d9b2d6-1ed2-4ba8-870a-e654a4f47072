import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import VLCPlayer from '../components/camera/VLCPlayer';

// Mock electron's ipcRenderer
const mockIpcRenderer = {
  invoke: jest.fn(),
};

// Mock the electron module
jest.mock('electron', () => ({
  ipcRenderer: mockIpcRenderer,
}));

describe('VLCPlayer', () => {
  beforeEach(() => {
    // Clear mock calls before each test
    mockIpcRenderer.invoke.mockClear();
  });

  it('renders loading state initially', () => {
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" />);
    expect(screen.getByText('Starting VLC stream...')).toBeInTheDocument();
  });

  it('shows error message when streamUrl is missing', () => {
    render(<VLCPlayer />);
    expect(screen.getByText('Missing stream URL')).toBeInTheDocument();
  });

  it('calls ipcRenderer.invoke with correct arguments when mounted', () => {
    const streamUrl = 'rtsp://example.com/stream';
    render(<VLCPlayer streamUrl={streamUrl} />);
    expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('start-vlc-stream', streamUrl);
  });

  it('shows success message when stream starts successfully', async () => {
    mockIpcRenderer.invoke.mockResolvedValueOnce({ success: true });
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" />);
    
    // Wait for the success message
    const successMessage = await screen.findByText('VLC stream is active');
    expect(successMessage).toBeInTheDocument();
  });

  it('shows error message when stream fails to start', async () => {
    const errorMessage = 'Failed to start VLC';
    mockIpcRenderer.invoke.mockResolvedValueOnce({ success: false, error: errorMessage });
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" />);
    
    // Wait for the error message
    const error = await screen.findByText(`Error starting VLC stream: ${errorMessage}`);
    expect(error).toBeInTheDocument();
  });

  it('calls stop-vlc-stream when stop button is clicked', async () => {
    mockIpcRenderer.invoke
      .mockResolvedValueOnce({ success: true }) // For start-vlc-stream
      .mockResolvedValueOnce({ success: true }); // For stop-vlc-stream
    
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" />);
    
    // Wait for the stream to start and show the stop button
    const stopButton = await screen.findByText('Stop Stream');
    fireEvent.click(stopButton);
    
    expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('stop-vlc-stream');
  });

  it('calls onError callback when error occurs', async () => {
    const onError = jest.fn();
    const errorMessage = 'Test error';
    mockIpcRenderer.invoke.mockResolvedValueOnce({ success: false, error: errorMessage });
    
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" onError={onError} />);
    
    // Wait for the error callback to be called
    await screen.findByText(`Error starting VLC stream: ${errorMessage}`);
    expect(onError).toHaveBeenCalledWith(`Error starting VLC stream: ${errorMessage}`);
  });

  it('calls onPlay callback when stream starts successfully', async () => {
    const onPlay = jest.fn();
    mockIpcRenderer.invoke.mockResolvedValueOnce({ success: true });
    
    render(<VLCPlayer streamUrl="rtsp://example.com/stream" onPlay={onPlay} />);
    
    // Wait for the success message
    await screen.findByText('VLC stream is active');
    expect(onPlay).toHaveBeenCalled();
  });
});