.detection-rule-set {
  background-color: #1a1a1a;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  overflow: auto;
}

.detection-rule-set-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
}

.detection-rule-set-header h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #b6e14b;
}

.success-message {
  background-color: rgba(46, 125, 50, 0.2);
  color: #4caf50;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 4px solid #4caf50;
}

.error-message {
  background-color: rgba(183, 28, 28, 0.2);
  color: #f44336;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 4px solid #f44336;
}

/* New grid layout for detection rules */
.detection-rules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.detection-rule-item {
  background-color: #2c2c2c;
  border-radius: 6px;
  padding: 12px 16px;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.detection-rule-item:hover {
  background-color: #333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.detection-rule-item.rule-enabled {
  border-left-color: #b6e14b;
  background-color: rgba(182, 225, 75, 0.1);
}

.detection-rule-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
}

.detection-rule-checkbox {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  cursor: pointer;
  accent-color: #b6e14b;
}

.detection-rule-name {
  font-size: 15px;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.loading-indicator {
  display: inline-block;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.detection-rule-set-loading,
.detection-rule-set-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: #999;
}

.detection-rule-set-error {
  color: #f44336;
}
