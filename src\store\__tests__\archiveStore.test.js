import { renderHook, act } from '@testing-library/react';
import { useArchiveStore } from '../archiveStore';
import archiveApi from '../../services/archiveApi';

// Mock the archive API
jest.mock('../../services/archiveApi');

describe('Archive Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset store state
    useArchiveStore.getState().clearData();
  });

  describe('Initial State', () => {
    test('has correct initial state', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      expect(result.current.recordings).toEqual([]);
      expect(result.current.availableStreams).toEqual([]);
      expect(result.current.selectedStreamId).toBeNull();
      expect(result.current.selectedRecording).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
      expect(result.current.filters).toEqual({
        dateRange: 'today',
        streamId: 'all',
        sortBy: 'newest'
      });
      expect(result.current.recordingStatus).toEqual({});
      expect(result.current.statusPollingInterval).toBeNull();
    });
  });

  describe('Loading State Management', () => {
    test('setLoading updates loading state', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      act(() => {
        result.current.setLoading(true);
      });
      
      expect(result.current.isLoading).toBe(true);
    });

    test('setError updates error state', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      act(() => {
        result.current.setError('Test error');
      });
      
      expect(result.current.error).toBe('Test error');
    });

    test('clearError clears error state', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      act(() => {
        result.current.setError('Test error');
        result.current.clearError();
      });
      
      expect(result.current.error).toBeNull();
    });
  });

  describe('Filter Management', () => {
    test('setFilters updates filters', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      act(() => {
        result.current.setFilters({ dateRange: 'week', sortBy: 'oldest' });
      });
      
      expect(result.current.filters).toEqual({
        dateRange: 'week',
        streamId: 'all',
        sortBy: 'oldest'
      });
    });
  });

  describe('Recording Management', () => {
    test('loadRecordings fetches and stores recordings', async () => {
      const mockRecordings = [
        {
          filename: '2025-01-15_00-00-00.mp4',
          stream_id: 'Eagle_*************',
          timestamp: '2025-01-15T00:00:00Z',
          size_bytes: 1048576
        }
      ];

      archiveApi.getRecordings.mockResolvedValue({
        recordings: mockRecordings,
        count: 1
      });

      const { result } = renderHook(() => useArchiveStore());
      
      await act(async () => {
        await result.current.loadRecordings('Eagle_*************');
      });
      
      expect(result.current.recordings).toEqual(mockRecordings);
      expect(result.current.selectedStreamId).toBe('Eagle_*************');
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    test('loadRecordings handles API errors', async () => {
      archiveApi.getRecordings.mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => useArchiveStore());
      
      await act(async () => {
        await result.current.loadRecordings('Eagle_*************');
      });
      
      expect(result.current.recordings).toEqual([]);
      expect(result.current.error).toBe('API Error');
      expect(result.current.isLoading).toBe(false);
    });

    test('loadRecordings requires stream ID', async () => {
      const { result } = renderHook(() => useArchiveStore());
      
      await act(async () => {
        await result.current.loadRecordings('');
      });
      
      expect(result.current.error).toBe('Stream ID is required');
    });
  });

  describe('Available Streams Management', () => {
    test('loadAvailableStreams fetches and stores streams', async () => {
      const mockStreams = [
        {
          stream_id: 'Eagle_*************',
          collection_name: 'Eagle',
          camera_ip: '*************',
          recording_count: 5
        }
      ];

      archiveApi.getAvailableStreams.mockResolvedValue({
        streams: mockStreams,
        count: 1
      });

      const { result } = renderHook(() => useArchiveStore());
      
      await act(async () => {
        await result.current.loadAvailableStreams();
      });
      
      expect(result.current.availableStreams).toEqual(mockStreams);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('Recording Deletion', () => {
    test('deleteRecording removes recording from list', async () => {
      const mockRecordings = [
        {
          filename: '2025-01-15_00-00-00.mp4',
          stream_id: 'Eagle_*************'
        },
        {
          filename: '2025-01-14_00-00-00.mp4',
          stream_id: 'Eagle_*************'
        }
      ];

      archiveApi.deleteRecording.mockResolvedValue({ status: 'success' });

      const { result } = renderHook(() => useArchiveStore());
      
      // Set initial recordings
      act(() => {
        result.current.recordings = mockRecordings;
      });

      await act(async () => {
        const success = await result.current.deleteRecording('Eagle_*************', '2025-01-15_00-00-00.mp4');
        expect(success).toBe(true);
      });
      
      expect(result.current.recordings).toHaveLength(1);
      expect(result.current.recordings[0].filename).toBe('2025-01-14_00-00-00.mp4');
    });
  });

  describe('Utility Functions', () => {
    test('getRecordingStreamUrl returns correct URL', () => {
      archiveApi.getRecordingStreamUrl.mockReturnValue('http://localhost:8000/api/archive/stream/Eagle_*************/file.mp4');

      const { result } = renderHook(() => useArchiveStore());
      
      const url = result.current.getRecordingStreamUrl('Eagle_*************', 'file.mp4');
      expect(url).toBe('http://localhost:8000/api/archive/stream/Eagle_*************/file.mp4');
    });

    test('getRecordingStats calculates statistics correctly', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      // Set test recordings
      act(() => {
        result.current.recordings = [
          {
            filename: '2025-01-15_00-00-00.mp4',
            timestamp: '2025-01-15T00:00:00Z',
            size_bytes: 1048576
          },
          {
            filename: '2025-01-14_00-00-00.mp4',
            timestamp: '2025-01-14T00:00:00Z',
            size_bytes: 2097152
          }
        ];
      });

      const stats = result.current.getRecordingStats();
      
      expect(stats.totalRecordings).toBe(2);
      expect(stats.totalSize).toBe(3145728); // 1MB + 2MB
      expect(stats.dateRange).toBeDefined();
    });

    test('getRecordingStats handles empty recordings', () => {
      const { result } = renderHook(() => useArchiveStore());
      
      const stats = result.current.getRecordingStats();
      
      expect(stats.totalRecordings).toBe(0);
      expect(stats.totalSize).toBe(0);
      expect(stats.totalSizeFormatted).toBe('0 B');
      expect(stats.dateRange).toBeNull();
    });
  });

  describe('Recording Status Management', () => {
    test('getRecordingStatus returns unknown for non-existent stream', () => {
      const { result } = renderHook(() => useArchiveStore());

      const status = result.current.getRecordingStatus('non_existent_stream');

      expect(status).toEqual({
        isRecording: false,
        lastUpdate: null,
        processActive: false,
        status: 'unknown'
      });
    });

    test('hasActiveRecordings returns false when no recordings', () => {
      const { result } = renderHook(() => useArchiveStore());

      expect(result.current.hasActiveRecordings()).toBe(false);
    });

    test('getActiveRecordingCount returns 0 when no recordings', () => {
      const { result } = renderHook(() => useArchiveStore());

      expect(result.current.getActiveRecordingCount()).toBe(0);
    });

    test('loadArchiveStatus updates recording status', async () => {
      const mockStatus = {
        status: 'active',
        stream_ids: ['Eagle_*************', 'Eagle_192.168.4.242'],
        active_recordings: 2
      };

      archiveApi.getArchiveStatus.mockResolvedValue(mockStatus);

      const { result } = renderHook(() => useArchiveStore());

      await act(async () => {
        await result.current.loadArchiveStatus();
      });

      expect(result.current.archiveStatus).toEqual(mockStatus);
      expect(Object.keys(result.current.recordingStatus)).toHaveLength(2);
      expect(result.current.recordingStatus['Eagle_*************'].isRecording).toBe(true);
      expect(result.current.recordingStatus['Eagle_192.168.4.242'].isRecording).toBe(true);
    });
  });

  describe('Data Clearing', () => {
    test('clearData resets all state including recording status', () => {
      const { result } = renderHook(() => useArchiveStore());

      // Set some data first
      act(() => {
        result.current.recordings = [{ filename: 'test.mp4' }];
        result.current.selectedStreamId = 'test_stream';
        result.current.setError('test error');
        result.current.recordingStatus = { 'test_stream': { isRecording: true } };
      });

      act(() => {
        result.current.clearData();
      });

      expect(result.current.recordings).toEqual([]);
      expect(result.current.availableStreams).toEqual([]);
      expect(result.current.selectedStreamId).toBeNull();
      expect(result.current.selectedRecording).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.recordingStatus).toEqual({});
      expect(result.current.statusPollingInterval).toBeNull();
    });
  });
});
