import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DraggableVideoGridCell from '../DraggableVideoGridCell';

// Mock react-dnd
jest.mock('react-dnd', () => ({
  useDrag: () => [{ isDragging: false }, jest.fn(), jest.fn()],
  useDrop: () => [{ isOver: false, canDrop: true }, jest.fn()],
  DndProvider: ({ children }) => children,
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {},
}));

// Mock the CameraCard component
jest.mock('../../camera/CameraCard', () => {
  return function MockCameraCard({ camera, onCameraClick }) {
    return (
      <div
        data-testid="camera-card"
        onClick={() => onCameraClick && onCameraClick(camera)}
      >
        {camera?.name || 'Mock Camera'}
      </div>
    );
  };
});

// Mock the camera store
jest.mock('../../../store/cameraStore', () => ({
  useCameraStore: () => ({
    swapCameras: jest.fn(),
  }),
}));

const mockCamera = {
  id: 'camera-1',
  name: 'Test Camera',
  streamUrl: 'http://test.com/stream',
  ip: '*************',
};

const renderWithDnd = (component) => {
  return render(component);
};

describe('DraggableVideoGridCell', () => {
  const defaultProps = {
    index: 0,
    onSwap: jest.fn(),
    onCameraClick: jest.fn(),
    gridKey: 'test-key',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Camera Cell', () => {
    it('renders camera cell with camera data', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      expect(screen.getByRole('gridcell')).toBeInTheDocument();
      expect(screen.getByTestId('camera-card')).toBeInTheDocument();
      expect(screen.getByLabelText(/Camera Test Camera, position 1/)).toBeInTheDocument();
    });

    it('has proper accessibility attributes', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      const cell = screen.getByRole('gridcell');
      expect(cell).toHaveAttribute('tabIndex', '0');
      expect(cell).toHaveAttribute('aria-grabbed', 'false');
      expect(cell).toHaveAttribute('aria-dropeffect', 'none');
    });

    it('calls onCameraClick when camera card is clicked', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      fireEvent.click(screen.getByTestId('camera-card'));
      expect(defaultProps.onCameraClick).toHaveBeenCalledWith(mockCamera);
    });

    it('handles keyboard navigation - arrow left', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={1}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowLeft' });
      expect(defaultProps.onSwap).toHaveBeenCalledWith(1, 0);
    });

    it('handles keyboard navigation - arrow right', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={0}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowRight' });
      expect(defaultProps.onSwap).toHaveBeenCalledWith(0, 1);
    });

    it('handles keyboard navigation - arrow up', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={2}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowUp' });
      expect(defaultProps.onSwap).toHaveBeenCalledWith(2, 0);
    });

    it('handles keyboard navigation - arrow down', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={0}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowDown' });
      expect(defaultProps.onSwap).toHaveBeenCalledWith(0, 2);
    });

    it('handles Enter key to select camera', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'Enter' });
      expect(defaultProps.onCameraClick).toHaveBeenCalledWith(mockCamera);
    });

    it('handles Space key to select camera', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: ' ' });
      expect(defaultProps.onCameraClick).toHaveBeenCalledWith(mockCamera);
    });

    it('prevents context menu during right-click drag', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
        />
      );

      const cell = screen.getByRole('gridcell');

      // Simulate right mouse button down
      fireEvent.mouseDown(cell, { button: 2 });

      // Simulate context menu event
      const contextMenuEvent = new MouseEvent('contextmenu', {
        bubbles: true,
        cancelable: true,
      });

      const preventDefaultSpy = jest.spyOn(contextMenuEvent, 'preventDefault');
      fireEvent(cell, contextMenuEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });
  });

  describe('Placeholder Cell', () => {
    it('renders placeholder cell', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          isPlaceholder={true}
        />
      );

      expect(screen.getByRole('gridcell')).toBeInTheDocument();
      expect(screen.getByText('No Camera Feed Available')).toBeInTheDocument();
      expect(screen.getByLabelText(/Empty camera slot 1/)).toBeInTheDocument();
    });

    it('does not handle keyboard events for placeholder', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          isPlaceholder={true}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowLeft' });
      expect(defaultProps.onSwap).not.toHaveBeenCalled();
    });

    it('does not call onCameraClick for placeholder', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          isPlaceholder={true}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'Enter' });
      expect(defaultProps.onCameraClick).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('does not move left from index 0', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={0}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowLeft' });
      expect(defaultProps.onSwap).not.toHaveBeenCalled();
    });

    it('does not move up from index less than 2', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={mockCamera}
          index={1}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'ArrowUp' });
      expect(defaultProps.onSwap).not.toHaveBeenCalled();
    });

    it('handles missing camera gracefully', () => {
      renderWithDnd(
        <DraggableVideoGridCell
          {...defaultProps}
          camera={null}
        />
      );

      const cell = screen.getByRole('gridcell');
      fireEvent.keyDown(cell, { key: 'Enter' });
      expect(defaultProps.onCameraClick).not.toHaveBeenCalled();
    });
  });
});
