import {
  generateCameraId,
  parseCameraId,
  normalizeCollectionName,
  denormalizeCollectionName,
  normalizeCameraId,
  parseStreamUrl
} from '../../utils/cameraUtils';

describe('cameraUtils', () => {
  describe('generateCameraId', () => {
    it('should generate a valid camera ID from collection name and IP', () => {
      const result = generateCameraId('Test Collection', '*************');
      expect(result).toBe('camera-test-collection-192-168-1-100');
    });

    it('should handle special characters in collection name', () => {
      const result = generateCameraId('Test & Collection!', '*************');
      expect(result).toBe('camera-test-collection-192-168-1-100');
    });
  });

  describe('parseCameraId', () => {
    it('should parse a valid camera ID into collection name and IP', () => {
      const result = parseCameraId('camera-test-collection-192-168-1-100');
      expect(result).toEqual({
        collectionName: 'Test Collection',
        normalizedCollectionName: 'test-collection',
        ip: '*************'
      });
    });

    it('should handle single word collection names with exact match from collections', () => {
      const collections = [{ name: 'k1' }];
      const result = parseCameraId('camera-k1-172-16-1-102', collections);
      expect(result).toEqual({
        collectionName: 'k1',
        normalizedCollectionName: 'k1',
        ip: '************'
      });
    });

    it('should handle collection names with multiple words', () => {
      const result = parseCameraId('camera-my-test-collection-192-168-1-100');
      expect(result).toEqual({
        collectionName: 'My Test Collection',
        normalizedCollectionName: 'my-test-collection',
        ip: '*************'
      });
    });

    it('should handle invalid camera IDs gracefully', () => {
      const result = parseCameraId('invalid-id');
      expect(result).toEqual({
        collectionName: '',
        normalizedCollectionName: '',
        ip: ''
      });
    });

    it('should handle camera IDs with insufficient parts', () => {
      const result = parseCameraId('camera-test-192');
      expect(result).toEqual({
        collectionName: '',
        normalizedCollectionName: '',
        ip: ''
      });
    });

    it('should handle Class A private IP range (10.0.0.0/8)', () => {
      const result = parseCameraId('camera-office-10-0-0-1');
      expect(result).toEqual({
        collectionName: 'Office',
        normalizedCollectionName: 'office',
        ip: '********'
      });
    });

    it('should handle Class B private IP range (**********/12)', () => {
      const result = parseCameraId('camera-warehouse-172-20-1-100');
      expect(result).toEqual({
        collectionName: 'Warehouse',
        normalizedCollectionName: 'warehouse',
        ip: '************'
      });
    });

    it('should handle Class C private IP range (***********/16)', () => {
      const result = parseCameraId('camera-home-network-192-168-1-254');
      expect(result).toEqual({
        collectionName: 'Home Network',
        normalizedCollectionName: 'home-network',
        ip: '*************'
      });
    });

    it('should handle edge case IPs like localhost', () => {
      const result = parseCameraId('camera-localhost-127-0-0-1');
      expect(result).toEqual({
        collectionName: 'Localhost',
        normalizedCollectionName: 'localhost',
        ip: '127.0.0.1'
      });
    });

    it('should match exact collection names when collections array is provided', () => {
      const collections = [
        { name: 'k1' },
        { name: 'My-Special_Collection!' },
        { name: 'Test Collection' }
      ];

      // Test exact match for k1
      const result1 = parseCameraId('camera-k1-192-168-1-100', collections);
      expect(result1.collectionName).toBe('k1');

      // Test exact match for special characters collection
      const result2 = parseCameraId('camera-my-special-collection--192-168-1-100', collections);
      expect(result2.collectionName).toBe('My-Special_Collection!');

      // Test exact match for multi-word collection
      const result3 = parseCameraId('camera-test-collection-192-168-1-100', collections);
      expect(result3.collectionName).toBe('Test Collection');
    });
  });

  describe('normalizeCollectionName', () => {
    it('should normalize collection names correctly', () => {
      expect(normalizeCollectionName('Test Collection')).toBe('test-collection');
      expect(normalizeCollectionName('Test & Collection!')).toBe('test-collection');
      expect(normalizeCollectionName(' Spaces  ')).toBe('spaces');
    });
  });

  describe('denormalizeCollectionName', () => {
    it('should denormalize collection names correctly', () => {
      expect(denormalizeCollectionName('test-collection')).toBe('Test Collection');
      expect(denormalizeCollectionName('spaces')).toBe('Spaces');
    });
  });

  describe('normalizeCameraId', () => {
    it('should normalize camera IDs correctly', () => {
      expect(normalizeCameraId('Camera-123')).toBe('camera-123');
      expect(normalizeCameraId(' Camera 123! ')).toBe('camera123');
    });
  });

  describe('parseStreamUrl', () => {
    it('should parse valid RTSP URLs correctly', () => {
      const result = parseStreamUrl('rtsp://admin:pass@*************:554/stream1');
      expect(result).toEqual({
        collectionName: 'stream1',
        ip: '*************'
      });
    });

    it('should handle empty URLs gracefully', () => {
      const result = parseStreamUrl('');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });

    it('should handle invalid URLs gracefully', () => {
      const result = parseStreamUrl('not-a-url');
      expect(result).toEqual({
        collectionName: '',
        ip: ''
      });
    });
  });
});