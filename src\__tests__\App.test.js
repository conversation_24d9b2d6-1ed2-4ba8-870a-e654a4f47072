import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';

// Mock react-dnd
jest.mock('react-dnd', () => ({
  DndProvider: ({ children }) => children
}));

jest.mock('react-dnd-html5-backend', () => ({
  HTML5Backend: {}
}));

import App from '../App';

// Mock the components used in App.js
jest.mock('../components/dashboard/Dashboard', () => {
  return function MockDashboard(props) {
    return (
      <div data-testid="dashboard-component">
        <button 
          data-testid="view-change-button" 
          onClick={() => props.onViewChange('map')}
        >
          Change View
        </button>
        <div data-testid="current-view">{props.currentView}</div>
        <div data-testid="collection-manager-status">
          {props.showCollectionManager ? 'Showing' : 'Hidden'}
        </div>
      </div>
    );
  };
});

jest.mock('../components/events/PerformanceMetrics', () => {
  return function MockPerformanceMetrics() {
    return <div data-testid="performance-metrics"></div>;
  };
});

jest.mock('../components/events/EventsSidebar', () => {
  return function MockEventsSidebar() {
    return <div data-testid="events-sidebar"></div>;
  };
});

jest.mock('../components/sidebar/MainSidebar', () => {
  return function MockMainSidebar(props) {
    return (
      <div data-testid="main-sidebar">
        <button 
          data-testid="create-collection-button" 
          onClick={props.onCreateCollection}
        >
          Create Collection
        </button>
      </div>
    );
  };
});

jest.mock('../components/events/EventsContent', () => {
  return function MockEventsContent() {
    return <div data-testid="events-content"></div>;
  };
});

jest.mock('../components/archive/ArchiveSidebar', () => {
  return function MockArchiveSidebar() {
    return <div data-testid="archive-sidebar"></div>;
  };
});

jest.mock('../components/configuration/ConfigurationSidebar', () => {
  return function MockConfigurationSidebar() {
    return <div data-testid="configuration-sidebar"></div>;
  };
});

jest.mock('../components/settings/SettingsSidebar', () => {
  return function MockSettingsSidebar() {
    return <div data-testid="settings-sidebar"></div>;
  };
});

jest.mock('../components/camera/CameraManager', () => {
  return {
    CameraProvider: ({ children }) => <div data-testid="camera-provider">{children}</div>
  };
});

describe('App Component', () => {
  test('App renders without crashing', () => {
    render(<App />);
    expect(screen.getByTestId('camera-provider')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
  });

  test('TabBar renders with correct tabs', () => {
    render(<App />);
    const tabs = ['Dashboard', 'Events', 'Archive', 'Configuration', 'Settings'];
    
    tabs.forEach(tab => {
      expect(screen.getByText(tab)).toBeInTheDocument();
    });
    
    expect(screen.getByTestId('performance-metrics')).toBeInTheDocument();
  });

  test('Tab switching works correctly', () => {
    render(<App />);
    
    // Initially Dashboard tab should be active
    const dashboardTab = screen.getAllByText('Dashboard').find(el => el.tagName.toLowerCase() === 'button');
    expect(dashboardTab).toHaveClass('active');
    
    // Click on Events tab
    const eventsTab = screen.getAllByText('Events').find(el => el.tagName.toLowerCase() === 'button');
    fireEvent.click(eventsTab);
    
    // Events tab should now be active
    expect(eventsTab).toHaveClass('active');
    expect(screen.getByTestId('events-sidebar')).toBeInTheDocument();
    
    // Click on Archive tab
    const archiveTab = screen.getAllByText('Archive').find(el => el.tagName.toLowerCase() === 'button');
    fireEvent.click(archiveTab);
    
    // Archive tab should now be active
    expect(archiveTab).toHaveClass('active');
    expect(screen.getByTestId('archive-sidebar')).toBeInTheDocument();
    
    // Click on Configuration tab
    const configTab = screen.getAllByText('Configuration').find(el => el.tagName.toLowerCase() === 'button');
    fireEvent.click(configTab);
    
    // Configuration tab should now be active
    expect(configTab).toHaveClass('active');
    expect(screen.getByTestId('configuration-sidebar')).toBeInTheDocument();
    
    // Click on Settings tab
    const settingsTab = screen.getAllByText('Settings').find(el => el.tagName.toLowerCase() === 'button');
    fireEvent.click(settingsTab);
    
    // Settings tab should now be active
    expect(settingsTab).toHaveClass('active');
    expect(screen.getByTestId('settings-sidebar')).toBeInTheDocument();
  });

  test('View switching works correctly', () => {
    render(<App />);
    
    // Initially view should be 'camera'
    expect(screen.getByTestId('current-view')).toHaveTextContent('camera');
    
    // Change view to 'map'
    fireEvent.click(screen.getByTestId('view-change-button'));
    
    // View should now be 'map'
    expect(screen.getByTestId('current-view')).toHaveTextContent('map');
    
    // Collection manager should be hidden when switching to non-camera view
    expect(screen.getByTestId('collection-manager-status')).toHaveTextContent('Hidden');
  });

  test('Collection manager toggle works', () => {
    render(<App />);
    
    // Initially collection manager should be hidden
    expect(screen.getByTestId('collection-manager-status')).toHaveTextContent('Hidden');
    
    // Click create collection button
    fireEvent.click(screen.getByTestId('create-collection-button'));
    
    // Collection manager should now be showing and view should be 'camera'
    expect(screen.getByTestId('collection-manager-status')).toHaveTextContent('Showing');
    expect(screen.getByTestId('current-view')).toHaveTextContent('camera');
  });

  test('handleCreateCollection works correctly', () => {
    render(<App />);
    
    // Change view to 'map' first
    fireEvent.click(screen.getByTestId('view-change-button'));
    expect(screen.getByTestId('current-view')).toHaveTextContent('map');
    
    // Click create collection button
    fireEvent.click(screen.getByTestId('create-collection-button'));
    
    // View should switch back to 'camera' and collection manager should be shown
    expect(screen.getByTestId('current-view')).toHaveTextContent('camera');
    expect(screen.getByTestId('collection-manager-status')).toHaveTextContent('Showing');
  });
});