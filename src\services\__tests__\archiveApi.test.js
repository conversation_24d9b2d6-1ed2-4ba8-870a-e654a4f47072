import archiveApi from '../archiveApi';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios;

describe('Archive API Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getRecordings', () => {
    test('fetches recordings successfully', async () => {
      const mockResponse = {
        data: {
          stream_id: 'Eagle_*************',
          recordings: [
            {
              filename: '2025-01-15_00-00-00.mp4',
              stream_id: 'Eagle_*************',
              timestamp: '2025-01-15T00:00:00Z',
              size_bytes: 1048576,
              size_mb: 1
            }
          ],
          count: 1
        }
      };

      mockedAxios.create.mockReturnValue({
        get: jest.fn().mockResolvedValue(mockResponse),
        interceptors: {
          request: { use: jest.fn() },
          response: { use: jest.fn() }
        }
      });

      const result = await archiveApi.getRecordings('Eagle_*************');
      expect(result).toEqual(mockResponse.data);
    });

    test('throws error for invalid stream ID', async () => {
      await expect(archiveApi.getRecordings('')).rejects.toThrow('Stream ID is required');
    });
  });

  describe('getRecordingStreamUrl', () => {
    test('generates correct streaming URL', () => {
      const url = archiveApi.getRecordingStreamUrl('Eagle_*************', '2025-01-15_00-00-00.mp4');
      expect(url).toBe('http://localhost:8000/api/archive/stream/Eagle_*************/2025-01-15_00-00-00.mp4');
    });

    test('throws error for missing parameters', () => {
      expect(() => archiveApi.getRecordingStreamUrl('', 'file.mp4')).toThrow('Stream ID and filename are required');
      expect(() => archiveApi.getRecordingStreamUrl('stream', '')).toThrow('Stream ID and filename are required');
    });
  });

  describe('parseStreamId', () => {
    test('parses stream ID correctly', () => {
      const result = archiveApi.parseStreamId('Eagle_*************');
      expect(result).toEqual({
        collectionName: 'Eagle',
        cameraIp: '*************'
      });
    });

    test('handles complex IP addresses', () => {
      const result = archiveApi.parseStreamId('Collection_192.168.1.100_backup');
      expect(result).toEqual({
        collectionName: 'Collection',
        cameraIp: '192.168.1.100_backup'
      });
    });

    test('throws error for invalid format', () => {
      expect(() => archiveApi.parseStreamId('invalid')).toThrow('Invalid stream ID format');
      expect(() => archiveApi.parseStreamId('')).toThrow('Invalid stream ID format');
    });
  });

  describe('formatRecordingDate', () => {
    test('formats date correctly', () => {
      const result = archiveApi.formatRecordingDate('2025-01-15T00:00:00Z');
      expect(result).toMatch(/Jan 15, 2025/); // Basic check for formatted date
    });

    test('handles invalid date', () => {
      const result = archiveApi.formatRecordingDate('invalid');
      expect(result).toBe('Invalid Date');
    });

    test('handles null/undefined', () => {
      expect(archiveApi.formatRecordingDate(null)).toBe('Unknown Date');
      expect(archiveApi.formatRecordingDate(undefined)).toBe('Unknown Date');
    });
  });

  describe('formatFileSize', () => {
    test('formats bytes correctly', () => {
      expect(archiveApi.formatFileSize(1024)).toBe('1 KB');
      expect(archiveApi.formatFileSize(1048576)).toBe('1 MB');
      expect(archiveApi.formatFileSize(1073741824)).toBe('1 GB');
    });

    test('handles zero and null', () => {
      expect(archiveApi.formatFileSize(0)).toBe('0 B');
      expect(archiveApi.formatFileSize(null)).toBe('0 B');
    });
  });

  describe('getRecordingsByDateRange', () => {
    test('filters recordings by date range', async () => {
      const mockRecordings = {
        recordings: [
          {
            filename: '2025-01-15_00-00-00.mp4',
            timestamp: '2025-01-15T00:00:00Z'
          },
          {
            filename: '2025-01-14_00-00-00.mp4',
            timestamp: '2025-01-14T00:00:00Z'
          },
          {
            filename: '2025-01-13_00-00-00.mp4',
            timestamp: '2025-01-13T00:00:00Z'
          }
        ]
      };

      // Mock getRecordings to return test data
      jest.spyOn(archiveApi, 'getRecordings').mockResolvedValue(mockRecordings);

      const startDate = new Date('2025-01-14T00:00:00Z');
      const endDate = new Date('2025-01-15T23:59:59Z');

      const result = await archiveApi.getRecordingsByDateRange('Eagle_*************', startDate, endDate);

      expect(result.recordings).toHaveLength(2);
      expect(result.recordings[0].filename).toBe('2025-01-15_00-00-00.mp4');
      expect(result.recordings[1].filename).toBe('2025-01-14_00-00-00.mp4');
    });
  });

  describe('getRecordingsForCamera', () => {
    test('constructs stream ID and fetches recordings', async () => {
      const mockRecordings = { recordings: [], count: 0 };
      jest.spyOn(archiveApi, 'getRecordings').mockResolvedValue(mockRecordings);

      await archiveApi.getRecordingsForCamera('Eagle', '*************');

      expect(archiveApi.getRecordings).toHaveBeenCalledWith('Eagle_*************');
    });

    test('throws error for missing parameters', async () => {
      await expect(archiveApi.getRecordingsForCamera('', '*************')).rejects.toThrow();
      await expect(archiveApi.getRecordingsForCamera('Eagle', '')).rejects.toThrow();
    });
  });
});
