import React, { useState, useEffect, useRef } from 'react';
import './EventsSidebar.css';
import '../sidebar/ModernSidebar.css'; // Import the new modern sidebar styles
import searchingIcon from '../../icon/searching.png';
import currentIcon from '../../icon/current.png';
import rulesIcon from '../../icon/rules.png';
import statisticsIcon from '../../icon/statistics.png';
import mediaIcon from '../../icon/media.png';
import monitoringIcon from '../../icon/monitoring.png';
import rulesSetIcon from '../../icon/rules-set.png';
import trackIcon from '../../icon/track.png';
import cameraIcon from '../../icon/camera.png';
import parameterIcon from '../../icon/parameter.png';
import syncIcon from '../../icon/sync.png';
import { useUserStore } from '../../store/userStore';

function EventsSidebar({ onMenuSelect }) {
  const [showDetectionRules, setShowDetectionRules] = useState(false);
  const currentUser = useUserStore(state => state.currentUser);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Ref for the sidebar element
  const sidebarRef = useRef(null);

  // Check if user is SuperAdmin
  const isSuperAdmin = currentUser && currentUser.role === 'SuperAdmin';

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Reset expanded state when switching between mobile and desktop
      if (!mobile) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar expansion for mobile view
  const toggleSidebar = () => {
    setSidebarExpanded(prev => !prev);
  };

  const handleMenuSelect = (menuId) => {
    if (onMenuSelect) {
      onMenuSelect(menuId);
    }
  };

  return (
    <>
      {isMobile && (
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? "✕" : "☰"}
        </button>
      )}
      <div
        ref={sidebarRef}
        className="universal-sidebar-content"
        role="navigation"
        aria-label="Events navigation"
      >
        <div className="sidebar-section">
          <button
            className="sidebar-btn"
            onClick={() => handleMenuSelect('search-events')}
          >
            <img src={searchingIcon} alt="" className="sidebar-icon" />
            <span>Search Events</span>
            {isMobile && !sidebarExpanded && (
              <span className="sidebar-tooltip">Search Events</span>
            )}
          </button>
        </div>
        <div className="sidebar-section">
          <button
            className="sidebar-btn"
            onClick={() => handleMenuSelect('current-events')}
          >
            <img src={currentIcon} alt="" className="sidebar-icon" />
            <span>Current Events</span>
            {isMobile && !sidebarExpanded && (
              <span className="sidebar-tooltip">Current Events</span>
            )}
          </button>
        </div>
      <div className="sidebar-section">
        <button
          className={`sidebar-btn ${showDetectionRules ? 'active' : ''}`}
          onClick={() => setShowDetectionRules((prev) => !prev)}
          aria-expanded={showDetectionRules}
          aria-controls="detection-rules-dropdown"
        >
          <img src={rulesIcon} alt="" className="sidebar-icon" />
          <span>Set Detection Rules</span>
          <span className={`chevron-icon ${showDetectionRules ? 'expanded' : ''}`}>
            {showDetectionRules ? "↑" : "↓"}
          </span>
          {isMobile && !sidebarExpanded && (
            <span className="sidebar-tooltip">Detection Rules</span>
          )}
        </button>
        {showDetectionRules && (
          <div id="detection-rules-dropdown" className="sidebar-dropdown">
            {/* Only show Detection Rule Set for SuperAdmin */}
            {isSuperAdmin && (
              <button
                className="sidebar-btn sidebar-btn-sub"
                onClick={() => handleMenuSelect('detection-rule-set')}
              >
                <img src={rulesSetIcon} alt="" className="sidebar-icon" />
                <span>Detection Rule Set</span>
              </button>
            )}
            <button
              className="sidebar-btn sidebar-btn-sub"
              onClick={() => handleMenuSelect('rules-on-camera')}
            >
              <img src={cameraIcon} alt="" className="sidebar-icon" />
              <span>Zone-Based Camera Rules</span>
            </button>
            <button
              className="sidebar-btn sidebar-btn-sub"
              onClick={() => handleMenuSelect('ptz-auto-tour')}
            >
              <img src={syncIcon} alt="" className="sidebar-icon" />
              <span>PTZ Auto Tour</span>
            </button>
            <button
              className="sidebar-btn sidebar-btn-sub"
              onClick={() => handleMenuSelect('ptz-auto-track')}
            >
              <img src={trackIcon} alt="" className="sidebar-icon" />
              <span>PTZ Auto Track</span>
            </button>
            <button
              className="sidebar-btn sidebar-btn-sub"
              onClick={() => handleMenuSelect('live-monitoring-rules')}
            >
              <img src={monitoringIcon} alt="" className="sidebar-icon" />
              <span>Live Monitoring Rules</span>
            </button>
            <button
              className="sidebar-btn sidebar-btn-sub"
              onClick={() => handleMenuSelect('event-parameters')}
            >
              <img src={parameterIcon} alt="" className="sidebar-icon" />
              <span>Event Parameters</span>
            </button>
          </div>
        )}
      </div>
      <div className="sidebar-section">
        <button
          className="sidebar-btn"
          onClick={() => handleMenuSelect('events-statistics')}
        >
          <img src={statisticsIcon} alt="" className="sidebar-icon" />
          <span>Events Statistics</span>
          {isMobile && !sidebarExpanded && (
            <span className="sidebar-tooltip">Events Statistics</span>
          )}
        </button>
      </div>
      {/* Uploaded Media Section - Commented out
      <div className="sidebar-section">
        <button className="sidebar-btn">
          <img src={mediaIcon} alt="Media" className="sidebar-icon" style={{ width: '16px', height: '20px' }} />
          Uploaded Media
        </button>
      </div>
      */}
      </div>
    </>
  );
}

export default EventsSidebar;