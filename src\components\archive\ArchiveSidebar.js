// React must be in scope when using JSX
import React, { useState, useEffect, useRef } from 'react';
import './ArchiveSidebar.css';
import '../sidebar/ModernSidebar.css'; // Import the new modern sidebar styles
import archiveIcon from '../../icon/archive.png';
import recReportIcon from '../../icon/rec-report.png';
import criticalIcon from '../../icon/critical.png';
import playbackIcon from '../../icon/playback.png';

const ArchiveSidebar = ({ onMenuSelect }) => {
  const [activeItem, setActiveItem] = useState('archive-playback');
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Ref for the sidebar element
  const sidebarRef = useRef(null);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Reset expanded state when switching between mobile and desktop
      if (!mobile) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar expansion for mobile view
  const toggleSidebar = () => {
    setSidebarExpanded(prev => !prev);
  };

  // Handle menu item selection
  useEffect(() => {
    if (onMenuSelect) {
      onMenuSelect(activeItem);
    }
  }, [activeItem, onMenuSelect]);

  const menuItems = [
    { id: 'archive-playback', label: 'Archive Playback', icon: archiveIcon },
    { id: 'recording-report', label: 'Recording Report', icon: recReportIcon },
    { id: 'critical-video', label: 'Critical Video', icon: criticalIcon },
    { id: 'redundant-playback', label: 'Redundant Playback', icon: playbackIcon }
  ];

  return (
    <>
      {isMobile && (
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? "✕" : "☰"}
        </button>
      )}
      <div
        ref={sidebarRef}
        className="universal-sidebar-content"
        role="navigation"
        aria-label="Archive navigation"
      >
        {/* Menu items */}
        {menuItems.map((item) => (
          <div key={item.id} className="sidebar-section">
            <button
              className={`sidebar-btn ${activeItem === item.id ? 'active' : ''}`}
              onClick={() => setActiveItem(item.id)}
            >
              <img src={item.icon} alt="" className="sidebar-icon" />
              <span>{item.label}</span>
              {isMobile && !sidebarExpanded && (
                <span className="sidebar-tooltip">{item.label}</span>
              )}
            </button>
          </div>
        ))}

      {/* No recordings section in sidebar anymore */}
      </div>
    </>
  );
};

// Use React.memo to optimize rendering
export default React.memo(ArchiveSidebar);