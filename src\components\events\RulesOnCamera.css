.rules-on-camera {
  padding: 20px;
  background-color: #1a1a1a;
  border-radius: 8px;
  color: #f0f0f0;
}

.rules-on-camera-header {
  margin-bottom: 20px;
}

.rules-on-camera-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
  color: #b6e14b;
}

.rules-on-camera-header p {
  font-size: 14px;
  color: #aaa;
}

.rules-on-camera-filters {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 16px;
  flex-wrap: wrap;
}

.search-filter {
  flex: 1;
  min-width: 200px;
}

.search-input {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #f0f0f0;
  font-size: 14px;
}

.area-filter {
  width: 180px;
}

.area-select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #444;
  background-color: #2c2c2c;
  color: #f0f0f0;
  font-size: 14px;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23f0f0f0' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
}

.select-all-container {
  display: flex;
  align-items: center;
}

.select-all-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.select-all-checkbox {
  margin-right: 8px;
  width: 18px;
  height: 18px;
  accent-color: #b6e14b;
}

.filter-apply-button {
  padding: 10px 16px;
  background-color: #b6e14b;
  color: #1a1a1a;
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s;
}

.filter-apply-button:hover {
  background-color: #c8f35c;
}

.filter-apply-button:disabled {
  background-color: #5a6e25;
  cursor: not-allowed;
  opacity: 0.7;
}

.success-message {
  background-color: rgba(182, 225, 75, 0.2);
  border-left: 4px solid #b6e14b;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #b6e14b;
}

.error-message {
  background-color: rgba(244, 67, 54, 0.2);
  border-left: 4px solid #f44336;
  padding: 12px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  color: #f44336;
}

.rules-on-camera-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
  font-size: 18px;
  color: #aaa;
}
