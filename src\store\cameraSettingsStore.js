import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useCameraSettingsStore = create(
  persist(
    (set, get) => ({
      // Camera settings state
      cameraSettings: {}, // { cameraIp: { resolution: {width, height}, onvifCredentials: {...} } }
      supportedResolutions: {}, // { cameraIp: [{width, height, label}] }
      currentResolutions: {}, // { cameraIp: {width, height} }
      connectionStatus: {}, // { cameraIp: 'connected' | 'disconnected' | 'connecting' | 'error' }
      
      // New video stream settings state
      currentStreamSettings: {}, // { cameraIp: { streamType, videoType, bitrateType, videoQuality, frameRate, maxBitrate, videoEncoding, h265Plus, profile, iFrameInterval, svc, smoothing } }
      supportedStreamSettings: {}, // { cameraIp: { streamTypes, videoTypes, bitrateTypes, videoQualities, frameRates, maxBitrates, videoEncodings, profiles, svcOptions } }
      
      // Common resolution presets
      commonResolutions: [
        { width: 1920, height: 1080, label: '1080p (1920x1080)' },
        { width: 1280, height: 720, label: '720p (1280x720)' },
        { width: 800, height: 600, label: 'SVGA (800x600)' },
        { width: 640, height: 480, label: 'VGA (640x480)' },
        { width: 320, height: 240, label: 'QVGA (320x240)' },
      ],

      // Common stream settings presets
      commonStreamSettings: {
        streamTypes: [
          { value: 'main', label: 'Main Stream (Normal)' },
          { value: 'sub', label: 'Sub Stream' }
        ],
        videoTypes: [
          { value: 'video', label: 'Video Stream' },
          { value: 'audio', label: 'Audio Stream' }
        ],
        bitrateTypes: [
          { value: 'variable', label: 'Variable' },
          { value: 'constant', label: 'Constant' }
        ],
        videoQualities: [
          { value: 'low', label: 'Low' },
          { value: 'medium', label: 'Medium' },
          { value: 'high', label: 'High' },
          { value: 'ultra', label: 'Ultra' }
        ],
        frameRates: [
          { value: 15, label: '15 fps' },
          { value: 25, label: '25 fps' },
          { value: 30, label: '30 fps' },
          { value: 60, label: '60 fps' }
        ],
        maxBitrates: [
          { value: 512, label: '512 Kbps' },
          { value: 1024, label: '1024 Kbps' },
          { value: 2048, label: '2048 Kbps' },
          { value: 4096, label: '4096 Kbps' },
          { value: 8192, label: '8192 Kbps' }
        ],
        videoEncodings: [
          { value: 'H.264', label: 'H.264' },
          { value: 'H.265', label: 'H.265' }
        ],
        h265PlusOptions: [
          { value: 'on', label: 'ON' },
          { value: 'off', label: 'OFF' }
        ],
        profiles: [
          { value: 'baseline', label: 'Baseline Profile' },
          { value: 'main', label: 'Main Profile' },
          { value: 'high', label: 'High Profile' }
        ],
        svcOptions: [
          { value: 'on', label: 'ON' },
          { value: 'off', label: 'OFF' }
        ]
      },

      // Actions
      setCameraSettings: (cameraIp, settings) =>
        set((state) => ({
          cameraSettings: {
            ...state.cameraSettings,
            [cameraIp]: {
              ...state.cameraSettings[cameraIp],
              ...settings,
            },
          },
        })),

      setSupportedResolutions: (cameraIp, resolutions) =>
        set((state) => ({
          supportedResolutions: {
            ...state.supportedResolutions,
            [cameraIp]: resolutions,
          },
        })),

      setCurrentResolution: (cameraIp, resolution) =>
        set((state) => ({
          currentResolutions: {
            ...state.currentResolutions,
            [cameraIp]: resolution,
          },
        })),

      setConnectionStatus: (cameraIp, status) =>
        set((state) => ({
          connectionStatus: {
            ...state.connectionStatus,
            [cameraIp]: status,
          },
        })),

      // New stream settings actions
      setCurrentStreamSettings: (cameraIp, settings) =>
        set((state) => ({
          currentStreamSettings: {
            ...state.currentStreamSettings,
            [cameraIp]: settings,
          },
        })),

      setSupportedStreamSettings: (cameraIp, settings) =>
        set((state) => ({
          supportedStreamSettings: {
            ...state.supportedStreamSettings,
            [cameraIp]: settings,
          },
        })),

      // Get camera settings for a specific IP
      getCameraSettings: (cameraIp) => {
        const state = get();
        return state.cameraSettings[cameraIp] || {};
      },

      // Get supported resolutions for a camera
      getSupportedResolutions: (cameraIp) => {
        const state = get();
        return state.supportedResolutions[cameraIp] || state.commonResolutions;
      },

      // Get current resolution for a camera
      getCurrentResolution: (cameraIp) => {
        const state = get();
        return state.currentResolutions[cameraIp] || null;
      },

      // Get connection status for a camera
      getConnectionStatus: (cameraIp) => {
        const state = get();
        return state.connectionStatus[cameraIp] || 'disconnected';
      },

      // New stream settings getters
      getCurrentStreamSettings: (cameraIp) => {
        const state = get();
        return state.currentStreamSettings[cameraIp] || null;
      },

      getSupportedStreamSettings: (cameraIp) => {
        const state = get();
        return state.supportedStreamSettings[cameraIp] || state.commonStreamSettings;
      },

      // Update ONVIF credentials for a camera
      updateOnvifCredentials: (cameraIp, credentials) =>
        set((state) => ({
          cameraSettings: {
            ...state.cameraSettings,
            [cameraIp]: {
              ...state.cameraSettings[cameraIp],
              onvifCredentials: credentials,
            },
          },
        })),

      // Clear settings for a camera
      clearCameraSettings: (cameraIp) =>
        set((state) => {
          const newSettings = { ...state.cameraSettings };
          const newSupportedResolutions = { ...state.supportedResolutions };
          const newCurrentResolutions = { ...state.currentResolutions };
          const newConnectionStatus = { ...state.connectionStatus };
          const newCurrentStreamSettings = { ...state.currentStreamSettings };
          const newSupportedStreamSettings = { ...state.supportedStreamSettings };

          delete newSettings[cameraIp];
          delete newSupportedResolutions[cameraIp];
          delete newCurrentResolutions[cameraIp];
          delete newConnectionStatus[cameraIp];
          delete newCurrentStreamSettings[cameraIp];
          delete newSupportedStreamSettings[cameraIp];

          return {
            cameraSettings: newSettings,
            supportedResolutions: newSupportedResolutions,
            currentResolutions: newCurrentResolutions,
            connectionStatus: newConnectionStatus,
            currentStreamSettings: newCurrentStreamSettings,
            supportedStreamSettings: newSupportedStreamSettings,
          };
        }),

      // Reset all settings
      resetAllSettings: () =>
        set({
          cameraSettings: {},
          supportedResolutions: {},
          currentResolutions: {},
          connectionStatus: {},
          currentStreamSettings: {},
          supportedStreamSettings: {},
        }),
    }),
    {
      name: 'camera-settings-storage',
      getStorage: () => localStorage,
    }
  )
);

export default useCameraSettingsStore;
