import { useCameraStore } from '../store/cameraStore';

// Mock electron's ipcRenderer
const mockIpcRenderer = {
  send: jest.fn(),
  on: jest.fn()
};

// Mock window.require to return our mock ipcRenderer
window.require = jest.fn().mockImplementation(() => ({
  ipcRenderer: mockIpcRenderer
}));

describe('Camera Store', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Reset the store to initial state
    const store = useCameraStore.getState();
    useCameraStore.setState({
      cameras: [],
      bookmarks: [],
      collections: [],
      activeCollection: null,
      currentLayout: 'grid',
      cameraJson: {}
    });
  });

  test('deleteCollection removes collection from state and calls saveCameraConfig', () => {
    // Setup initial state with a collection
    const collectionName = 'TestCollection';
    const collectionId = useCameraStore.getState().createCollection(collectionName);
    
    // Add a camera to the collection in cameraJson
    useCameraStore.setState({
      cameraJson: {
        [collectionName]: {
          '************': 'rtsp://admin:password@************:554'
        }
      }
    });
    
    // Verify initial state
    expect(useCameraStore.getState().collections.length).toBe(1);
    expect(useCameraStore.getState().cameraJson[collectionName]).toBeDefined();
    
    // Delete the collection
    useCameraStore.getState().deleteCollection(collectionId);
    
    // Verify collection is removed from state
    expect(useCameraStore.getState().collections.length).toBe(0);
    expect(useCameraStore.getState().cameraJson[collectionName]).toBeUndefined();
    
    // Verify saveCameraConfig was called
    expect(mockIpcRenderer.send).toHaveBeenCalledWith('save-camera-config', expect.any(String));
  });

  test('removeCameraFromCollection removes camera and calls saveCameraConfig', () => {
    // Setup initial state with a collection
    const collectionName = 'TestCollection';
    const collectionId = useCameraStore.getState().createCollection(collectionName);
    
    // Add a camera to the collection
    const cameraIp = '************';
    const streamUrl = 'rtsp://admin:password@************:554';
    
    // Setup cameraJson
    useCameraStore.setState({
      cameraJson: {
        [collectionName]: {
          [cameraIp]: streamUrl
        }
      }
    });
    
    // Add camera to the store
    const cameraId = `camera-${collectionName}-${cameraIp.replace(/\./g, '-')}`;
    useCameraStore.setState(state => ({
      cameras: [
        ...state.cameras,
        {
          id: cameraId,
          name: `${collectionName} (${cameraIp})`,
          streamUrl,
          position: 0,
          collectionId
        }
      ],
      collections: state.collections.map(c => 
        c.id === collectionId 
          ? { ...c, cameras: [...c.cameras, cameraId] } 
          : c
      )
    }));
    
    // Verify initial state
    expect(useCameraStore.getState().cameras.length).toBe(1);
    expect(useCameraStore.getState().collections[0].cameras.length).toBe(1);
    expect(useCameraStore.getState().cameraJson[collectionName][cameraIp]).toBe(streamUrl);
    
    // Remove camera from collection
    useCameraStore.getState().removeCameraFromCollection(cameraId, collectionId);
    
    // Verify camera is removed from collection but still exists in cameras array
    expect(useCameraStore.getState().cameras.length).toBe(1);
    expect(useCameraStore.getState().cameras[0].collectionId).toBeNull();
    expect(useCameraStore.getState().collections[0].cameras.length).toBe(0);
    
    // Verify camera is removed from cameraJson
    expect(useCameraStore.getState().cameraJson[collectionName][cameraIp]).toBeUndefined();
    
    // Verify saveCameraConfig was called
    expect(mockIpcRenderer.send).toHaveBeenCalledWith('save-camera-config', expect.any(String));
  });
});