import {
  isValidIPFormat,
  isPrivateIP,
  validatePrivateIP,
  extractIPFromStreamURL
} from '../../utils/ipValidation';

describe('ipValidation', () => {
  describe('isValidIPFormat', () => {
    it('should validate correct IP formats', () => {
      expect(isValidIPFormat('***********')).toBe(true);
      expect(isValidIPFormat('********')).toBe(true);
      expect(isValidIPFormat('************')).toBe(true);
      expect(isValidIPFormat('***************')).toBe(true);
      expect(isValidIPFormat('0.0.0.0')).toBe(true);
    });

    it('should reject invalid IP formats', () => {
      expect(isValidIPFormat('256.1.1.1')).toBe(false);
      expect(isValidIPFormat('192.168.1')).toBe(false);
      expect(isValidIPFormat('***********.1')).toBe(false);
      expect(isValidIPFormat('************')).toBe(false);
      expect(isValidIPFormat('not.an.ip.address')).toBe(false);
      expect(isValidIPFormat('')).toBe(false);
    });
  });

  describe('isPrivateIP', () => {
    it('should identify Class C private IPs (192.168.x.x)', () => {
      expect(isPrivateIP('***********')).toBe(true);
      expect(isPrivateIP('***********')).toBe(true);
      expect(isPrivateIP('***************')).toBe(true);
    });

    it('should identify Class A private IPs (10.x.x.x)', () => {
      expect(isPrivateIP('********')).toBe(true);
      expect(isPrivateIP('**************')).toBe(true);
      expect(isPrivateIP('********')).toBe(true);
    });

    it('should identify Class B private IPs (172.16.x.x - 172.31.x.x)', () => {
      expect(isPrivateIP('**********')).toBe(true);
      expect(isPrivateIP('**************')).toBe(true);
      expect(isPrivateIP('************')).toBe(true);
    });

    it('should reject public IPs', () => {
      expect(isPrivateIP('*******')).toBe(false);
      expect(isPrivateIP('*******')).toBe(false);
      expect(isPrivateIP('**********')).toBe(false); // Just outside Class B range
      expect(isPrivateIP('**********')).toBe(false); // Just outside Class B range
      expect(isPrivateIP('***********')).toBe(false); // Not 192.168
    });

    it('should reject invalid IP formats', () => {
      expect(isPrivateIP('invalid')).toBe(false);
      expect(isPrivateIP('')).toBe(false);
      expect(isPrivateIP('256.1.1.1')).toBe(false);
    });
  });

  describe('validatePrivateIP', () => {
    it('should validate private IPs successfully', () => {
      const result = validatePrivateIP('***********00');
      expect(result.isValid).toBe(true);
      expect(result.error).toBeNull();
    });

    it('should reject empty or null IPs', () => {
      expect(validatePrivateIP('').isValid).toBe(false);
      expect(validatePrivateIP('   ').isValid).toBe(false);
      expect(validatePrivateIP(null).isValid).toBe(false);
    });

    it('should reject invalid IP formats with appropriate error', () => {
      const result = validatePrivateIP('invalid.ip');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('valid IP address format');
    });

    it('should reject public IPs with appropriate error', () => {
      const result = validatePrivateIP('*******');
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('private network ranges');
    });
  });

  describe('extractIPFromStreamURL', () => {
    it('should extract IP from RTSP URLs', () => {
      expect(extractIPFromStreamURL('rtsp://admin:password@***********00:554/stream'))
        .toBe('***********00');
      expect(extractIPFromStreamURL('rtsp://user:pass@*********:554/'))
        .toBe('*********');
    });

    it('should extract IP from HTTP URLs', () => {
      expect(extractIPFromStreamURL('http://*************:8080/video'))
        .toBe('*************');
    });

    it('should return null for domain names', () => {
      expect(extractIPFromStreamURL('rtsp://camera.example.com:554/stream'))
        .toBeNull();
    });

    it('should handle malformed URLs by regex fallback', () => {
      expect(extractIPFromStreamURL('malformed://************/stream'))
        .toBe('************');
    });

    it('should return null for empty or invalid URLs', () => {
      expect(extractIPFromStreamURL('')).toBeNull();
      expect(extractIPFromStreamURL(null)).toBeNull();
      expect(extractIPFromStreamURL('not-a-url')).toBeNull();
    });
  });
});
