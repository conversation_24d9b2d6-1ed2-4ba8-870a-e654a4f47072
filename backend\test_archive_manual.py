#!/usr/bin/env python3
"""
Manual test script for the Archive Recording System

This script allows you to test the archive recording functionality
without starting the full VMS application.

Usage:
    python test_archive_manual.py [--test-mode]

Options:
    --test-mode    Run in test mode with mock RTSP streams
"""

import sys
import time
import json
import tempfile
import argparse
from pathlib import Path

# Add the backend directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from archive_manager import ArchiveRecordingManager


def create_test_config():
    """Create a test camera configuration"""
    config_data = {
        "TestCollection": {
            "*************": "rtsp://test:test@*************:554/stream1",
            "*************": "rtsp://test:test@*************:554/stream2"
        },
        "AnotherCollection": {
            "*********": "rtsp://admin:admin@*********:554/live"
        }
    }
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(config_data, f, indent=2)
        return f.name


def test_archive_manager_basic():
    """Test basic archive manager functionality"""
    print("=" * 60)
    print("TESTING ARCHIVE MANAGER - BASIC FUNCTIONALITY")
    print("=" * 60)
    
    # Create test configuration
    config_file = create_test_config()
    recordings_dir = tempfile.mkdtemp()
    
    print(f"Test config file: {config_file}")
    print(f"Test recordings dir: {recordings_dir}")
    
    try:
        # Initialize manager
        print("\n1. Initializing Archive Manager...")
        manager = ArchiveRecordingManager(
            camera_config_path=config_file,
            recordings_base_path=recordings_dir
        )
        print("✓ Archive Manager initialized successfully")
        
        # Test configuration loading
        print("\n2. Testing configuration loading...")
        config = manager._load_camera_config()
        print(f"✓ Loaded configuration with {len(config)} collections")
        for collection, cameras in config.items():
            print(f"  - {collection}: {len(cameras)} cameras")
        
        # Test stream ID generation
        print("\n3. Testing stream ID generation...")
        stream_id = manager._get_stream_id("TestCollection", "*************")
        print(f"✓ Generated stream ID: {stream_id}")
        
        # Test directory creation
        print("\n4. Testing recording directory creation...")
        stream_dir = manager._create_recording_directory(stream_id)
        print(f"✓ Created directory: {stream_dir}")
        
        # Test filename generation
        print("\n5. Testing filename generation...")
        filename = manager._get_current_filename()
        print(f"✓ Generated filename: {filename}")
        
        # Test status
        print("\n6. Testing status retrieval...")
        status = manager.get_status()
        print("✓ Status retrieved:")
        for key, value in status.items():
            print(f"  - {key}: {value}")
        
        # Test available recordings (empty)
        print("\n7. Testing available recordings (empty)...")
        recordings = manager.get_available_recordings(stream_id)
        print(f"✓ Available recordings: {len(recordings)} (expected: 0)")
        
        # Create test recording files
        print("\n8. Creating test recording files...")
        test_files = [
            "2025-01-15_00-00-00.mp4",
            "2025-01-14_00-00-00.mp4",
            "2025-01-13_00-00-00.mp4"
        ]
        
        for filename in test_files:
            test_file = stream_dir / filename
            test_file.write_bytes(b"fake video content for testing")
            print(f"  ✓ Created: {filename}")
        
        # Test available recordings (with files)
        print("\n9. Testing available recordings (with files)...")
        recordings = manager.get_available_recordings(stream_id)
        print(f"✓ Available recordings: {len(recordings)}")
        for recording in recordings:
            print(f"  - {recording}")
        
        # Test recording info
        print("\n10. Testing recording info retrieval...")
        for filename in test_files[:2]:  # Test first 2 files
            info = manager.get_recording_info(stream_id, filename)
            if info:
                print(f"✓ Info for {filename}:")
                print(f"  - Size: {info['size_mb']} MB")
                print(f"  - Timestamp: {info['timestamp']}")
            else:
                print(f"✗ Failed to get info for {filename}")
        
        # Test recording path
        print("\n11. Testing recording path retrieval...")
        path = manager.get_recording_path(stream_id, test_files[0])
        if path and path.exists():
            print(f"✓ Recording path: {path}")
        else:
            print("✗ Failed to get recording path")
        
        print("\n" + "=" * 60)
        print("BASIC FUNCTIONALITY TESTS COMPLETED SUCCESSFULLY")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            manager.stop_all_recordings()
            Path(config_file).unlink(missing_ok=True)
            import shutil
            shutil.rmtree(recordings_dir, ignore_errors=True)
            print("\n✓ Cleanup completed")
        except:
            pass
    
    return True


def test_archive_manager_recording(test_mode=True):
    """Test archive manager recording functionality"""
    print("=" * 60)
    print("TESTING ARCHIVE MANAGER - RECORDING FUNCTIONALITY")
    print("=" * 60)
    
    if test_mode:
        print("Running in TEST MODE (no actual RTSP recording)")
    else:
        print("Running in LIVE MODE (will attempt actual RTSP recording)")
        print("WARNING: This requires valid RTSP streams!")
    
    # Use real config if not in test mode
    if test_mode:
        config_file = create_test_config()
    else:
        config_file = "data/camera_configuration.json"
        if not Path(config_file).exists():
            print(f"✗ Configuration file not found: {config_file}")
            return False
    
    recordings_dir = tempfile.mkdtemp()
    
    print(f"Config file: {config_file}")
    print(f"Recordings dir: {recordings_dir}")
    
    try:
        # Initialize manager
        print("\n1. Initializing Archive Manager...")
        manager = ArchiveRecordingManager(
            camera_config_path=config_file,
            recordings_base_path=recordings_dir
        )
        print("✓ Archive Manager initialized successfully")
        
        if test_mode:
            print("\n2. Skipping actual recording start (test mode)")
            print("✓ In production, this would start FFmpeg processes")
        else:
            print("\n2. Starting recording processes...")
            print("⚠️  This will start actual FFmpeg processes!")
            
            response = input("Continue? (y/N): ")
            if response.lower() != 'y':
                print("Aborted by user")
                return True
            
            manager.start_all_recordings()
            print("✓ Recording processes started")
            
            # Let it run for a short time
            print("\n3. Letting recordings run for 10 seconds...")
            for i in range(10):
                time.sleep(1)
                print(f"  {i+1}/10 seconds...")
            
            # Check status
            print("\n4. Checking recording status...")
            status = manager.get_status()
            print("Recording status:")
            for key, value in status.items():
                print(f"  - {key}: {value}")
        
        print("\n" + "=" * 60)
        print("RECORDING FUNCTIONALITY TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n✗ Error during recording test: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup
        try:
            manager.stop_all_recordings()
            if test_mode:
                Path(config_file).unlink(missing_ok=True)
            import shutil
            shutil.rmtree(recordings_dir, ignore_errors=True)
            print("\n✓ Cleanup completed")
        except:
            pass
    
    return True


def main():
    """Main test function"""
    parser = argparse.ArgumentParser(description="Test Archive Recording System")
    parser.add_argument(
        "--test-mode", 
        action="store_true", 
        help="Run in test mode with mock data"
    )
    parser.add_argument(
        "--recording-test", 
        action="store_true", 
        help="Test recording functionality"
    )
    
    args = parser.parse_args()
    
    print("VMS Archive Recording System - Manual Test")
    print("=" * 60)
    
    success = True
    
    # Run basic functionality tests
    if not test_archive_manager_basic():
        success = False
    
    # Run recording tests if requested
    if args.recording_test:
        print("\n")
        if not test_archive_manager_recording(test_mode=args.test_mode):
            success = False
    
    print("\n" + "=" * 60)
    if success:
        print("ALL TESTS COMPLETED SUCCESSFULLY! ✓")
        print("\nThe Archive Recording System appears to be working correctly.")
        print("You can now start the VMS backend to begin automatic recording.")
    else:
        print("SOME TESTS FAILED! ✗")
        print("\nPlease check the error messages above and fix any issues.")
    print("=" * 60)
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
