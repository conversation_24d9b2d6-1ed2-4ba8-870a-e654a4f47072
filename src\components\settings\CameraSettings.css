.camera-settings {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  color: black;
}

.settings-header {
  margin-bottom: 2rem;
  border-bottom: 1px solid #ddd;
  padding-bottom: 1rem;
}

.settings-header h2 {
  color: black;
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
}

.settings-header p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

.settings-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.settings-section {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.settings-section h3 {
  color: black;
  margin: 0 0 1rem 0;
  font-size: 1.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 0.5rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: black;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: black;
  font-size: 0.9rem;
  transition: border-color 0.2s ease;
}

.form-control:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.form-control:disabled {
  background: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr 120px;
  gap: 1rem;
  margin-bottom: 1rem;
}

.credentials-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  text-decoration: none;
  display: inline-block;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.button-group {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.connection-status {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: capitalize;
  margin-top: 0.5rem;
}

.status-disconnected {
  background: #6c757d;
  color: white;
}

.status-connecting {
  background: #ffc107;
  color: #212529;
}

.status-connected {
  background: #28a745;
  color: white;
}

.status-error {
  background: #dc3545;
  color: white;
}

.current-resolution {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 1.1rem;
  font-weight: 500;
}

.resolution-label {
  color: #007bff;
  font-size: 0.9rem;
  font-weight: normal;
}

.resolution-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.alert {
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  font-size: 0.9rem;
}

.alert-error {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid #dc3545;
  color: #dc3545;
}

.alert-success {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid #28a745;
  color: #28a745;
}

/* Responsive Design */
@media (max-width: 768px) {
  .camera-settings {
    padding: 1rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .settings-section {
    padding: 1rem;
  }

  .current-resolution {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Loading States */
.btn:disabled {
  position: relative;
}

.btn:disabled::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Hover Effects */
.settings-section:hover {
  border-color: #ccc;
  background: #fafafa;
  transition: all 0.2s ease;
}

.form-control:hover:not(:disabled) {
  border-color: #aaa;
}

/* Focus States */
.btn:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.form-control:focus {
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Selection Styles */
select.form-control {
  cursor: pointer;
}

select.form-control option {
  background: white;
  color: black;
}

/* Status Indicators */
.connection-status::before {
  content: '●';
  margin-right: 0.5rem;
  font-size: 1.2em;
}

.status-connected::before {
  color: #28a745;
}

.status-connecting::before {
  color: #ffc107;
  animation: pulse 1.5s infinite;
}

.status-error::before {
  color: #dc3545;
}

.status-disconnected::before {
  color: #6c757d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Credential Hints */
.credential-hints {
  margin-top: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.credential-hints h4 {
  color: #007bff;
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.credential-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.credential-option {
  padding: 0.5rem 1rem;
  background: white;
  color: black;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.credential-option:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.credential-note {
  color: #666;
  font-size: 0.85rem;
  margin: 0;
  font-style: italic;
}

/* Video Stream Settings Styles */
.current-stream-settings {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 1rem;
  font-weight: 500;
}

.current-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.current-settings-grid span {
  font-size: 0.9rem;
  font-weight: normal;
  color: #007bff;
  padding: 0.25rem 0.5rem;
  background: white;
  border-radius: 3px;
  border: 1px solid #e9ecef;
}

.stream-settings-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/* Slider Styles */
input[type="range"].form-control {
  padding: 0.5rem 0;
  background: transparent;
  border: none;
  box-shadow: none;
}

input[type="range"].form-control:focus {
  box-shadow: none;
}

.slider-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #666;
}

/* Number input specific styles */
input[type="number"].form-control {
  text-align: center;
}

/* Responsive adjustments for stream settings */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }
  
  .current-settings-grid {
    grid-template-columns: 1fr;
  }
}
