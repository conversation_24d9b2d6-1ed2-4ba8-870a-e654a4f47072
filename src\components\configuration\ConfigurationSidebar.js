import React, { useState, useEffect, useRef } from 'react';
import './ConfigurationSidebar.css';
import '../sidebar/ModernSidebar.css'; // Import the new modern sidebar styles
import userIcon from '../../icon/user.png';
import storageIcon from '../../icon/storage.png';
import cctvIcon from '../../icon/cctv-camera.png';
import alertIcon from '../../icon/alert.png';
import mapIcon from '../../icon/map.png';
import externalIcon from '../../icon/external.png';
import drSitesIcon from '../../icon/dr-sites.png';
import trackSettingsIcon from '../../icon/track-settings.png';
import eventIcon from '../../icon/event.png';
import mailIcon from '../../icon/mail.png';
import senderIcon from '../../icon/sender.png';
import videoDecoderIcon from '../../icon/video-decoder.png';
import analyticIcon from '../../icon/analytic.png';
import mediaServerIcon from '../../icon/media-server.png';
import storage2Icon from '../../icon/storage-2.png';
import pinIcon from '../../icon/pin (1).png';
import basicMapIcon from '../../icon/map.png';
import secureAccessIcon from '../../icon/secure-access.png';

import addIcon from '../../icon/add-icon.png';
import AddCollectionModal from './AddCollectionModal';

const ConfigurationSidebar = ({ onViewChange, currentUser }) => {
  const [activeItem, setActiveItem] = useState(null);
  const [expandedItems, setExpandedItems] = useState({});
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [sidebarExpanded, setSidebarExpanded] = useState(false);

  // Ref for the sidebar element
  const sidebarRef = useRef(null);

  // Handle window resize for responsive design
  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Reset expanded state when switching between mobile and desktop
      if (!mobile) {
        setSidebarExpanded(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Toggle sidebar expansion for mobile view
  const toggleSidebar = () => {
    setSidebarExpanded(prev => !prev);
  };

  const toggleDropdown = (itemId) => {
    setExpandedItems(prev => ({
      ...prev,
      [itemId]: !prev[itemId]
    }));
  };

  const handleMenuItemClick = (itemId) => {
    setActiveItem(itemId);
    if (itemId === 'cameras') {
      onViewChange('cameras');
    } else {
      onViewChange(null); // Reset view when selecting other menu items
    }
    if (menuItems.find(item => item.id === itemId)?.hasDropdown) {
      toggleDropdown(itemId);
    }
  };

  const handleDropdownItemClick = (parentId, dropdownItem) => {
    // Convert dropdown item label to a view identifier
    const viewId = dropdownItem.label.toLowerCase().replace(/\s+/g, '-');
    console.log('Dropdown item clicked:', viewId);
    onViewChange(viewId);
    setActiveItem(`${parentId}-${viewId}`);
  };

  // Check if user has permission to manage users
  console.log('Current user in ConfigurationSidebar:', currentUser);

  // Always allow Admin users to manage users (Supervisors)
  const canManageUsers = currentUser && (
    currentUser.role === 'SuperAdmin' ||
    currentUser.role === 'Admin'
  );

  const menuItems = [
    {
      id: 'users',
      label: 'Users',
      icon: userIcon,
      hasDropdown: true,
      dropdownItems: [
        { label: 'User Access Levels', icon: secureAccessIcon }
      ]
    },
    {
      id: 'storage-server',
      label: 'Storage & Server',
      icon: storageIcon,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Storage', icon: storage2Icon },
        { label: 'Media Server', icon: mediaServerIcon },
        { label: 'Analytics Server', icon: analyticIcon },
        { label: 'Video Decoder Connector', icon: videoDecoderIcon }
      ]
    },
    {
      id: 'cameras',
      label: 'Cameras',
      icon: cctvIcon,
      hasDropdown: false
    },
    {
      id: 'alerts',
      label: 'Alerts',
      icon: alertIcon,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Sender Configuration', icon: senderIcon },
        { label: 'Receiver Configuration', icon: mailIcon },
        { label: 'Event Distribution', icon: eventIcon }
      ]
    },
    {
      id: 'map',
      label: 'Map',
      icon: mapIcon,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Basic Map', icon: basicMapIcon },
        { label: 'Google Map', icon: pinIcon }
      ]
    },
    {
      id: 'dr-sites',
      label: 'DR Sites',
      icon: drSitesIcon,
      hasDropdown: true,
      dropdownItems: [
        { label: 'Replication Policy', icon: drSitesIcon }
      ]
    },
    // {
    //   id: 'track-settings',
    //   label: 'Track Settings',
    //   icon: trackSettingsIcon,
    //   hasDropdown: false
    // }
  ];

  return (
    <>
      {isMobile && (
        <button
          className="sidebar-toggle"
          onClick={toggleSidebar}
          aria-label={sidebarExpanded ? "Collapse sidebar" : "Expand sidebar"}
        >
          {sidebarExpanded ? "✕" : "☰"}
        </button>
      )}
      <div
        ref={sidebarRef}
        className="universal-sidebar-content"
        role="navigation"
        aria-label="Configuration navigation"
      >
        {menuItems.map((item) => {
          // Skip the Users section if user doesn't have permission to manage users
          if (item.id === 'users' && !canManageUsers) {
            return null;
          }

          return (
            <div key={item.id} className="sidebar-section">
              <button
                className={`sidebar-btn ${activeItem === item.id ? 'active' : ''}`}
                onClick={() => handleMenuItemClick(item.id)}
                aria-expanded={item.hasDropdown ? !!expandedItems[item.id] : undefined}
                aria-controls={item.hasDropdown ? `${item.id}-dropdown` : undefined}
              >
                <img src={item.icon} alt="" className="sidebar-icon" />
                <span>{item.label}</span>
                {item.hasDropdown && (
                  <span className={`chevron-icon ${expandedItems[item.id] ? 'expanded' : ''}`}>
                    {expandedItems[item.id] ? "↑" : "↓"}
                  </span>
                )}
                {isMobile && !sidebarExpanded && (
                  <span className="sidebar-tooltip">{item.label}</span>
                )}
              </button>
            {item.hasDropdown && expandedItems[item.id] && (
              <div id={`${item.id}-dropdown`} className="sidebar-dropdown">
                {item.dropdownItems.map((dropdownItem, index) => (
                  <div
                    key={index}
                    className="dropdown-item"
                    onClick={() => handleDropdownItemClick(item.id, dropdownItem)}
                    role="button"
                    tabIndex={0}
                  >
                    <img src={dropdownItem.icon} alt="" className="sidebar-icon" />
                    <span>{dropdownItem.label}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
      </div>
    </>
  );
};

export default ConfigurationSidebar;