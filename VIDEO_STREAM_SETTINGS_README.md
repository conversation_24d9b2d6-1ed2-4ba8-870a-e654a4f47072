# Video Stream Settings - VMS Enhancement

## Overview

This enhancement adds comprehensive video stream settings management to the Video Management System (VMS), allowing users to configure various camera parameters through a user-friendly interface. The implementation follows the same dynamic pattern as the existing resolution settings.

## New Features Added

### 1. Video Stream Settings Configuration

The following settings are now available for configuration:

| Setting | Type | Options/Range | Default |
|---------|------|---------------|---------|
| **Stream Type** | Dropdown | Main Stream (Normal), Sub Stream | Main Stream |
| **Video Type** | Dropdown | Video Stream, Audio Stream | Video Stream |
| **Resolution** | Dropdown | 1920x1080, 1280x720, 800x600, 640x480, 320x240 | 1280x720 |
| **Bitrate Type** | Dropdown | Variable, Constant | Variable |
| **Video Quality** | Dropdown | Low, Medium, High, Ultra | Medium |
| **Frame Rate** | Dropdown | 15 fps, 25 fps, 30 fps, 60 fps | 25 fps |
| **Max Bitrate** | Dropdown | 512 Kbps, 1024 Kbps, 2048 Kbps, 4096 Kbps, 8192 Kbps | 2048 Kbps |
| **Video Encoding** | Dropdown | H.264, H.265 | H.264 |
| **H.265+** | Dropdown | ON, OFF | OFF |
| **Profile** | Dropdown | Baseline Profile, Main Profile, High Profile | Main Profile |
| **I Frame Interval** | Number Input | 1-100 | 50 |
| **SVC** | Dropdown | ON, OFF | OFF |
| **Smoothing** | Slider | 0-100 (Clear → Smooth) | 50 |

### 2. Dynamic UI Components

- **Current Settings Display**: Shows all current camera settings in a clean, organized grid
- **Settings Grid**: Responsive grid layout for all configuration controls
- **Real-time Updates**: Settings are applied immediately when changed
- **Loading States**: Visual feedback during configuration changes
- **Error Handling**: Comprehensive error messages and validation

### 3. Backend API Endpoints

New ONVIF-based API endpoints have been added:

#### Get Current Stream Settings
```
GET /api/camera/{camera_ip}/current-stream-settings
```
Returns the current stream settings for a camera.

#### Get Supported Stream Settings
```
GET /api/camera/{camera_ip}/supported-stream-settings
```
Returns all supported options for each setting.

#### Set Stream Settings
```
POST /api/camera/set-stream-settings
```
Applies new stream settings to the camera.

### 4. State Management

Enhanced Zustand store with new state management for:
- Current stream settings per camera
- Supported stream settings per camera
- Dynamic loading and caching of settings

## Implementation Details

### Frontend Changes

#### 1. Camera Settings Store (`src/store/cameraSettingsStore.js`)
- Added `currentStreamSettings` and `supportedStreamSettings` state
- Added common stream settings presets
- Added getter and setter methods for stream settings

#### 2. Camera Settings API (`src/services/cameraSettingsApi.js`)
- Added `getCurrentStreamSettings()` method
- Added `getSupportedStreamSettings()` method
- Added `setCameraStreamSettings()` method

#### 3. Camera Settings Component (`src/components/settings/settings/CameraSettings.js`)
- Added state variables for all new settings
- Added comprehensive UI controls for each setting
- Added `changeStreamSettings()` function
- Enhanced `loadCameraInfo()` to load stream settings
- Added responsive settings grid layout

#### 4. CSS Styling (`src/components/settings/CameraSettings.css`)
- Added styles for stream settings grid
- Added slider and number input styling
- Added responsive design for mobile devices
- Added current settings display styling

### Backend Changes

#### 1. Camera Settings Routes (`backend/camera_settings.py`)
- Added `StreamSettingsRequest` data model
- Added `/current-stream-settings` endpoint
- Added `/supported-stream-settings` endpoint
- Added `/set-stream-settings` endpoint

#### 2. ONVIF Service (`backend/services/onvif_service.py`)
- Added `get_current_stream_settings()` method
- Added `get_supported_stream_settings()` method
- Added `set_camera_stream_settings()` method
- Added `_get_default_supported_settings()` helper method

## Usage Instructions

### 1. Accessing Stream Settings

1. Navigate to the Settings page in the VMS
2. Select a camera from the dropdown
3. Enter ONVIF credentials and test connection
4. Once connected, the "Video Stream Settings" section will appear

### 2. Configuring Settings

1. **View Current Settings**: The current camera settings are displayed at the top
2. **Select New Settings**: Use the dropdowns and controls to select desired values
3. **Apply Changes**: Click "Apply Stream Settings" to save changes
4. **Monitor Status**: Watch for success/error messages

### 3. Supported Settings by Camera

Not all cameras support all settings. The system will:
- Show only supported options in dropdowns
- Apply only supported settings
- Provide feedback on which settings were successfully applied

## Technical Architecture

### Data Flow

1. **Frontend** → User selects camera and settings
2. **API Call** → Frontend calls backend API
3. **ONVIF Service** → Backend communicates with camera via ONVIF
4. **Camera** → Camera applies settings and confirms
5. **Response** → Success/failure message returned to frontend
6. **State Update** → Frontend updates local state and UI

### Error Handling

- **Connection Errors**: Automatic retry with different ONVIF ports
- **Setting Errors**: Individual setting validation and fallback
- **Camera Errors**: Graceful degradation with default values
- **Network Errors**: Timeout handling and user feedback

### Performance Considerations

- **Caching**: Camera connections and settings are cached
- **Async Operations**: All ONVIF operations are asynchronous
- **Connection Pooling**: Reuses camera connections when possible
- **Timeout Management**: Configurable timeouts for different operations

## Testing

### Manual Testing

1. **Test with Real Camera**: Use the test script `test_stream_settings.py`
2. **UI Testing**: Test all dropdowns, sliders, and buttons
3. **Error Scenarios**: Test with invalid credentials, network issues
4. **Responsive Design**: Test on different screen sizes

### Test Script

```bash
# Run the test script (update camera IP first)
python test_stream_settings.py
```

## Future Enhancements

### Planned Features

1. **Bulk Settings**: Apply settings to multiple cameras at once
2. **Settings Templates**: Save and reuse common configurations
3. **Advanced Settings**: More granular control over encoding parameters
4. **Settings History**: Track and revert setting changes
5. **Auto-Detection**: Automatically detect optimal settings for each camera

### Potential Improvements

1. **Real-time Preview**: Show video preview with applied settings
2. **Settings Validation**: Validate settings before applying
3. **Performance Metrics**: Monitor impact of settings on performance
4. **Settings Export/Import**: Backup and restore camera configurations

## Troubleshooting

### Common Issues

1. **Settings Not Applied**: Check camera ONVIF support and credentials
2. **Slow Response**: Increase timeout values in configuration
3. **Missing Options**: Camera may not support all settings
4. **Connection Errors**: Verify network connectivity and camera IP

### Debug Information

Enable debug logging to see detailed ONVIF communication:
```python
logging.getLogger('onvif_service').setLevel(logging.DEBUG)
```

## Conclusion

This enhancement significantly improves the VMS's camera configuration capabilities, providing users with fine-grained control over video stream parameters. The implementation follows established patterns and maintains compatibility with existing functionality while adding powerful new features.

The modular design allows for easy extension and maintenance, and the comprehensive error handling ensures a robust user experience even when dealing with various camera models and network conditions. 