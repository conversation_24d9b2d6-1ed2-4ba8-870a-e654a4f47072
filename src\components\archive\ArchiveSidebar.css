.sidebar {
  width: 250px;
  height: 100%;
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 20px 0;
  border-right: 1px solid #333;
}

.sidebar-section {
  padding: 0 15px;
  margin-bottom: 10px;
}

.sidebar-btn {
  width: 100%;
  padding: 10px 15px;
  background: none;
  border: none;
  color: #cccccc;
  text-align: left;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-btn:hover {
  background-color: #333;
  color: #ffffff;
}

.sidebar-btn.active {
  background-color: #444;
  color: #ffffff;
}

.sidebar-icon {
  width: 16px;
  height: 20px;
  object-fit: contain;
}

.sidebar-header {
  padding: 0 20px 20px 20px;
  border-bottom: 1px solid #333;
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #ffffff;
}

.sidebar-menu {
  padding: 20px 0;
}

.menu-item {
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.menu-item:hover {
  background-color: #333;
}

.menu-label {
  font-size: 0.9rem;
  color: #cccccc;
}

.menu-item:hover .menu-label {
  color: #ffffff;
}

/* Recordings section */
.recordings-section {
  margin-top: 20px;
  padding: 0 15px;
}

.camera-filter {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.camera-filter label {
  color: #cccccc;
  font-size: 0.9rem;
}

.camera-select {
  background-color: #333;
  color: #ffffff;
  border: 1px solid #444;
  padding: 5px 10px;
  border-radius: 4px;
  flex: 1;
}

.recordings-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.no-recordings {
  padding: 20px 0;
  text-align: center;
  color: #999;
}

.no-recordings .hint {
  font-size: 0.8rem;
  margin-top: 10px;
  color: #777;
}

.recording-item {
  display: flex;
  padding: 10px;
  border-bottom: 1px solid #333;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.recording-item:hover {
  background-color: #2a2a2a;
}

.recording-thumbnail {
  width: 60px;
  height: 40px;
  background-color: #333;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.thumbnail-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: #222;
}

.camera-icon {
  font-size: 1.2rem;
}

.recording-details {
  flex: 1;
  min-width: 0;
}

.recording-camera {
  margin: 0 0 5px 0;
  font-size: 0.9rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.recording-time {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: #aaa;
  margin-bottom: 3px;
}

.recording-ip {
  font-size: 0.75rem;
  color: #777;
}

.recording-actions {
  display: flex;
  flex-direction: column;
  gap: 5px;
  justify-content: center;
}

.play-button, .delete-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.play-button {
  background-color: #2a6b2a;
}

.play-button:hover {
  background-color: #3c8c3c;
}

.delete-button {
  background-color: #6b2a2a;
}

.delete-button:hover {
  background-color: #8c3c3c;
}