.event-statistics {
  background-color: #1a1a1a;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;
}

.event-statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #333;
}

.event-statistics-header h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #b6e14b;
}

.chart-type-selector {
  display: flex;
  gap: 10px;
}

.chart-type-btn {
  background-color: #2c2c2c;
  color: #fff;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.chart-type-btn.active {
  background-color: #b6e14b;
  color: #000;
}

.chart-type-btn:hover:not(.active) {
  background-color: #3c3c3c;
}

.event-statistics-filters {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.rules-filter,
.camera-filter {
  flex: 1;
  background-color: #2c2c2c;
  padding: 15px;
  border-radius: 6px;
}

.rules-filter h3,
.camera-filter h3 {
  font-size: 1rem;
  margin-top: 0;
  margin-bottom: 10px;
  color: #b6e14b;
}

.rules-list {
  max-height: 200px;
  overflow-y: auto;
  padding-right: 10px;
}

.rule-checkbox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.rule-checkbox input[type="checkbox"] {
  margin-right: 8px;
  width: 16px;
  height: 16px;
  accent-color: #b6e14b;
}

.camera-filter select {
  width: 100%;
  padding: 8px;
  background-color: #1a1a1a;
  color: #fff;
  border: 1px solid #444;
  border-radius: 4px;
}

.event-statistics-visualization {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2c2c2c;
  border-radius: 6px;
  padding: 20px;
  min-height: 300px;
}

.no-data-message {
  color: #999;
  font-size: 1.1rem;
  text-align: center;
}

.chart-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pie-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.pie-chart svg {
  width: 200px;
  height: 200px;
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

.bar-chart {
  width: 100%;
}

.bars-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.bar-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bar-label {
  width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bar-container {
  flex: 1;
  height: 24px;
  background-color: #1a1a1a;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.bar {
  height: 100%;
  transition: width 0.3s ease;
}

.bar-value {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-weight: 500;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.8);
}

.event-statistics-loading,
.event-statistics-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 1.2rem;
  color: #999;
}

.event-statistics-error {
  color: #f44336;
}
