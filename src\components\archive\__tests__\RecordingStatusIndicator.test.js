import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import RecordingStatusIndicator, { 
  OverallRecordingStatus, 
  CameraRecordingStatus,
  useRecordingStatus 
} from '../RecordingStatusIndicator';

// Mock the archive store
jest.mock('../../../store/archiveStore', () => ({
  useArchiveStore: () => ({
    getRecordingStatus: jest.fn((streamId) => {
      if (streamId === 'Eagle_*************') {
        return {
          isRecording: true,
          lastUpdate: Date.now(),
          processActive: true,
          status: 'recording'
        };
      } else if (streamId === 'Eagle_*************') {
        return {
          isRecording: false,
          lastUpdate: Date.now(),
          processActive: false,
          status: 'stopped'
        };
      }
      return {
        isRecording: false,
        lastUpdate: null,
        processActive: false,
        status: 'unknown'
      };
    }),
    archiveStatus: {
      status: 'active',
      active_recordings: 2,
      recording_threads: 2,
      stream_ids: ['Eagle_*************', 'Eagle_192.168.4.244']
    },
    getActiveRecordingCount: jest.fn(() => 2),
    hasActiveRecordings: jest.fn(() => true),
    isLoading: false
  })
}));

describe('RecordingStatusIndicator', () => {
  test('renders recording status for active stream', () => {
    render(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={true}
        size="medium"
      />
    );
    
    expect(screen.getByText('Recording')).toBeInTheDocument();
    expect(screen.getByLabelText('Recording status: Recording')).toBeInTheDocument();
  });

  test('renders stopped status for inactive stream', () => {
    render(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={true}
        size="medium"
      />
    );
    
    expect(screen.getByText('Stopped')).toBeInTheDocument();
    expect(screen.getByLabelText('Recording status: Stopped')).toBeInTheDocument();
  });

  test('renders unknown status for non-existent stream', () => {
    render(
      <RecordingStatusIndicator 
        streamId="unknown_stream" 
        showLabel={true}
        size="medium"
      />
    );
    
    expect(screen.getByText('Unknown')).toBeInTheDocument();
    expect(screen.getByLabelText('Recording status: Unknown')).toBeInTheDocument();
  });

  test('renders without label when showLabel is false', () => {
    render(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={false}
        size="small"
      />
    );
    
    expect(screen.queryByText('Recording')).not.toBeInTheDocument();
    expect(screen.getByLabelText('Recording status: Recording')).toBeInTheDocument();
  });

  test('returns null when no streamId provided', () => {
    const { container } = render(
      <RecordingStatusIndicator 
        streamId={null} 
        showLabel={true}
        size="medium"
      />
    );
    
    expect(container.firstChild).toBeNull();
  });

  test('applies correct size classes', () => {
    const { rerender } = render(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={true}
        size="small"
      />
    );
    
    expect(screen.getByLabelText('Recording status: Recording')).toHaveClass('size-small');
    
    rerender(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={true}
        size="large"
      />
    );
    
    expect(screen.getByLabelText('Recording status: Recording')).toHaveClass('size-large');
  });
});

describe('OverallRecordingStatus', () => {
  test('renders active recording status', () => {
    render(<OverallRecordingStatus />);
    
    expect(screen.getByText('Recording 2 streams')).toBeInTheDocument();
    expect(screen.getByText('(2 processes running)')).toBeInTheDocument();
  });

  test('renders loading state', () => {
    // Mock loading state
    const { useArchiveStore } = require('../../../store/archiveStore');
    useArchiveStore.mockReturnValue({
      ...useArchiveStore(),
      isLoading: true,
      archiveStatus: null
    });

    render(<OverallRecordingStatus />);
    
    expect(screen.getByText('Checking status...')).toBeInTheDocument();
  });

  test('renders error state when archive status unavailable', () => {
    // Mock error state
    const { useArchiveStore } = require('../../../store/archiveStore');
    useArchiveStore.mockReturnValue({
      ...useArchiveStore(),
      isLoading: false,
      archiveStatus: null,
      hasActiveRecordings: jest.fn(() => false),
      getActiveRecordingCount: jest.fn(() => 0)
    });

    render(<OverallRecordingStatus />);
    
    expect(screen.getByText('Archive system unavailable')).toBeInTheDocument();
  });

  test('renders inactive state when no recordings', () => {
    // Mock inactive state
    const { useArchiveStore } = require('../../../store/archiveStore');
    useArchiveStore.mockReturnValue({
      ...useArchiveStore(),
      archiveStatus: { active_recordings: 0 },
      hasActiveRecordings: jest.fn(() => false),
      getActiveRecordingCount: jest.fn(() => 0)
    });

    render(<OverallRecordingStatus />);
    
    expect(screen.getByText('No active recordings')).toBeInTheDocument();
  });
});

describe('CameraRecordingStatus', () => {
  const mockCameras = [
    { 
      id: 'cam1', 
      name: 'Camera 1', 
      collection: 'Eagle', 
      ip: '*************' 
    },
    { 
      id: 'cam2', 
      name: 'Camera 2', 
      collection: 'Eagle', 
      ip: '*************' 
    }
  ];

  test('renders camera recording status list', () => {
    render(<CameraRecordingStatus cameras={mockCameras} />);
    
    expect(screen.getByText('Camera Recording Status')).toBeInTheDocument();
    expect(screen.getByText('Camera 1')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();
    expect(screen.getByText('Camera 2')).toBeInTheDocument();
    expect(screen.getByText('*************')).toBeInTheDocument();
  });

  test('renders empty state when no cameras', () => {
    render(<CameraRecordingStatus cameras={[]} />);
    
    expect(screen.getByText('No cameras configured')).toBeInTheDocument();
  });

  test('shows recording status for each camera', () => {
    render(<CameraRecordingStatus cameras={mockCameras} />);
    
    // Should have recording status indicators for each camera
    const statusIndicators = screen.getAllByLabelText(/Recording status:/);
    expect(statusIndicators).toHaveLength(2);
  });
});

describe('useRecordingStatus hook', () => {
  test('returns recording status for stream', () => {
    const TestComponent = ({ streamId }) => {
      const status = useRecordingStatus(streamId);
      return <div data-testid="status">{status.status}</div>;
    };

    render(<TestComponent streamId="Eagle_*************" />);
    
    expect(screen.getByTestId('status')).toHaveTextContent('recording');
  });

  test('returns unknown status for invalid stream', () => {
    const TestComponent = ({ streamId }) => {
      const status = useRecordingStatus(streamId);
      return <div data-testid="status">{status.status}</div>;
    };

    render(<TestComponent streamId="invalid_stream" />);
    
    expect(screen.getByTestId('status')).toHaveTextContent('unknown');
  });
});

describe('Accessibility', () => {
  test('recording status indicator has proper ARIA labels', () => {
    render(
      <RecordingStatusIndicator 
        streamId="Eagle_*************" 
        showLabel={true}
        size="medium"
      />
    );
    
    const indicator = screen.getByLabelText('Recording status: Recording');
    expect(indicator).toHaveAttribute('title', 'Recording Status: Recording');
  });

  test('overall status has proper structure', () => {
    render(<OverallRecordingStatus />);
    
    const statusElement = screen.getByText('Recording 2 streams').closest('.overall-recording-status');
    expect(statusElement).toBeInTheDocument();
    expect(statusElement).toHaveClass('active');
  });

  test('camera status list has proper structure', () => {
    const mockCameras = [
      { 
        id: 'cam1', 
        name: 'Camera 1', 
        collection: 'Eagle', 
        ip: '*************' 
      }
    ];

    render(<CameraRecordingStatus cameras={mockCameras} />);
    
    expect(screen.getByRole('heading', { level: 4 })).toHaveTextContent('Camera Recording Status');
  });
});
