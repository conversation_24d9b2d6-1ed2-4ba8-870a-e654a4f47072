import React, { useState } from 'react';
import BasicMap from './BasicMap';
import GlobalMap from './GlobalMap';
import './Maps.css';
import mapIcon from '../../icon/map.png';
import pinIcon from '../../icon/pin (1).png';

const MapSelector = ({ onMapSelect, selectedMap }) => {
  return (
    <div className="map-selector">
      <button 
        className={`sidebar-btn sidebar-btn-sub ${selectedMap === 'basic' ? 'active' : ''}`}
        onClick={() => onMapSelect('basic')}
      >
        <img src={mapIcon} alt="Basic Map" className="sidebar-icon" style={{ width: '3px', height: '20px' }} />
        Basic Map
      </button>
      <button 
        className={`sidebar-btn sidebar-btn-sub ${selectedMap === 'global' ? 'active' : ''}`}
        onClick={() => onMapSelect('global')}
      >
        <img src={pinIcon} alt="Global Map" className="sidebar-icon" style={{ width: '3px', height: '23px' }} />
        Global Map
      </button>
    </div>
  );
};

export default MapSelector;