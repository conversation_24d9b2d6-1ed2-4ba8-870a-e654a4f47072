import { ipcRenderer } from '../../services/electronService';

describe('electronService', () => {
  const originalWindow = { ...window };
  
  afterEach(() => {
    // Restore window object after each test
    global.window = { ...originalWindow };
  });

  it('should provide mock implementation in web environment', async () => {
    // Ensure we're in web environment
    global.window.process = undefined;

    // Mock console.warn to avoid cluttering test output
    const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

    // Attempt to use ipcRenderer
    await expect(ipcRenderer.invoke('test-channel'))
      .rejects
      .toThrow('This feature is only available in the desktop app');

    expect(consoleSpy).toHaveBeenCalledWith('IPC call "test-channel" not supported in web environment');
    
    consoleSpy.mockRestore();
  });

  it('should use electron ipcRenderer in electron environment', () => {
    // Mock electron environment
    const mockIpcRenderer = {
      invoke: jest.fn()
    };
    
    global.window.process = { type: 'renderer' };
    global.window.require = jest.fn().mockReturnValue({ ipcRenderer: mockIpcRenderer });

    // Re-import to get electron version
    jest.resetModules();
    const { ipcRenderer } = require('../../services/electronService');

    ipcRenderer.invoke('test-channel');
    expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('test-channel');
  });
});