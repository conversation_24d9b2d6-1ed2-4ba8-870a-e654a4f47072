#!/usr/bin/env python3
"""
Test script for the new video stream settings functionality
"""

import asyncio
import sys
import os

# Add the backend directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from services.onvif_service import onvif_service

async def test_stream_settings():
    """Test the new stream settings functionality"""
    
    # Test camera credentials (replace with actual camera IP)
    test_ip = "*************"  # Replace with actual camera IP
    username = "admin"
    password = "admin"
    port = 80
    
    print("Testing Video Stream Settings Functionality")
    print("=" * 50)
    
    try:
        # Test 1: Get current stream settings
        print("\n1. Testing get_current_stream_settings...")
        current_settings = await onvif_service.get_current_stream_settings(test_ip, username, password, port)
        if current_settings:
            print("✓ Current stream settings retrieved successfully:")
            for key, value in current_settings.items():
                print(f"  {key}: {value}")
        else:
            print("✗ Could not retrieve current stream settings")
        
        # Test 2: Get supported stream settings
        print("\n2. Testing get_supported_stream_settings...")
        supported_settings = await onvif_service.get_supported_stream_settings(test_ip, username, password, port)
        if supported_settings:
            print("✓ Supported stream settings retrieved successfully:")
            for key, options in supported_settings.items():
                print(f"  {key}: {len(options)} options available")
        else:
            print("✗ Could not retrieve supported stream settings")
        
        # Test 3: Test setting stream settings (frame rate and bitrate only)
        print("\n3. Testing set_camera_stream_settings...")
        success = await onvif_service.set_camera_stream_settings(
            test_ip, username, password, port,
            frame_rate=25,
            max_bitrate=2048
        )
        if success:
            print("✓ Stream settings applied successfully")
        else:
            print("✗ Could not apply stream settings")
            
    except Exception as e:
        print(f"✗ Error during testing: {str(e)}")
    
    print("\n" + "=" * 50)
    print("Test completed!")

if __name__ == "__main__":
    asyncio.run(test_stream_settings()) 